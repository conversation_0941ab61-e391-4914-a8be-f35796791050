import { CheckCircle, XCircle, Loader2 } from 'lucide-react';

export type ConnectionStatus = 'disconnected' | 'connected' | 'testing';

interface ConnectionStatusProps {
  status: ConnectionStatus;
  error?: string | null;
}

export function ConnectionStatus({ status, error }: ConnectionStatusProps) {
  const getStatusIcon = () => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'testing':
        return <Loader2 className="w-4 h-4 text-blue-600 animate-spin" />;
      case 'disconnected':
        return <XCircle className="w-4 h-4 text-red-600" />;
      default:
        return null;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'connected':
        return 'Connected';
      case 'testing':
        return 'Testing connection...';
      case 'disconnected':
        return error ? 'Connection failed' : 'Disconnected';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'connected':
        return 'text-green-600';
      case 'testing':
        return 'text-blue-600';
      case 'disconnected':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="flex items-center gap-2">
      {getStatusIcon()}
      <span className={`text-sm font-medium ${getStatusColor()}`}>
        {getStatusText()}
      </span>
      {error && status === 'disconnected' && (
        <span className="text-xs text-red-500">({error})</span>
      )}
    </div>
  );
}
