'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { 
  Terminal, 
  Send, 
  ArrowUp, 
  ArrowDown, 
  AlertCircle, 
  CheckCircle,
  Command as CommandIcon,
  Loader2
} from 'lucide-react';
import { CommandSuggestion, CommandValidationResult, ParsedCommand } from '@/types/command';

interface CommandInputProps {
  onExecute: (command: string, args: string) => Promise<void>;
  placeholder?: string;
  disabled?: boolean;
  suggestions?: CommandSuggestion[];
  history?: string[];
  className?: string;
  showValidation?: boolean;
  autoFocus?: boolean;
}

export function CommandInput({
  onExecute,
  placeholder = 'Enter command...',
  disabled = false,
  suggestions = [],
  history = [],
  className = '',
  showValidation = true,
  autoFocus = false
}: CommandInputProps) {
  const [value, setValue] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState(0);
  const [validation, setValidation] = useState<CommandValidationResult | null>(null);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Auto-focus if requested
  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  // Validate command as user types
  useEffect(() => {
    if (showValidation && value.trim()) {
      const validationResult = validateCommand(value);
      setValidation(validationResult);
    } else {
      setValidation(null);
    }
  }, [value, showValidation]);

  // Filter suggestions based on input
  const filteredSuggestions = suggestions.filter(suggestion =>
    value.trim() && (
      suggestion.name.toLowerCase().includes(value.toLowerCase()) ||
      suggestion.description?.toLowerCase().includes(value.toLowerCase())
    )
  ).slice(0, 5); // Limit to 5 suggestions

  // Update suggestions visibility
  useEffect(() => {
    setShowSuggestions(filteredSuggestions.length > 0 && value.trim().length > 0);
    setSelectedSuggestion(0);
  }, [filteredSuggestions.length, value]);

  const parseCommand = (input: string): ParsedCommand => {
    const trimmed = input.trim();
    const parts = trimmed.split(/\s+/);
    const command = parts[0] || '';
    const args = parts.slice(1);
    const flags: Record<string, string | boolean> = {};
    const commandArgs: string[] = [];

    // Parse flags and arguments
    for (let i = 0; i < args.length; i++) {
      const arg = args[i];
      if (arg.startsWith('--')) {
        const [key, value] = arg.substring(2).split('=');
        flags[key] = value || true;
      } else if (arg.startsWith('-')) {
        flags[arg.substring(1)] = true;
      } else {
        commandArgs.push(arg);
      }
    }

    return {
      command,
      arguments: commandArgs,
      flags,
      raw: trimmed
    };
  };

  const validateCommand = (input: string): CommandValidationResult => {
    const parsed = parseCommand(input);
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // Basic validation
    if (!parsed.command) {
      errors.push('Command is required');
    }

    // Check if command starts with /
    if (parsed.command && !parsed.command.startsWith('/')) {
      warnings.push('Commands should start with /');
      suggestions.push(`Try: /${parsed.command}`);
    }

    // Check for common typos
    const commonCommands = ['help', 'clear', 'history', 'init', 'status'];
    const command = parsed.command.replace('/', '');
    if (command && !commonCommands.includes(command)) {
      const similar = commonCommands.find(cmd => 
        cmd.includes(command) || command.includes(cmd)
      );
      if (similar) {
        suggestions.push(`Did you mean: /${similar}?`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions
    };
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (disabled || isExecuting) return;

    switch (e.key) {
      case 'Enter':
        e.preventDefault();
        if (showSuggestions && selectedSuggestion < filteredSuggestions.length) {
          // Use selected suggestion
          const suggestion = filteredSuggestions[selectedSuggestion];
          setValue(suggestion.template);
          setShowSuggestions(false);
        } else {
          // Execute command
          handleExecute();
        }
        break;

      case 'ArrowUp':
        e.preventDefault();
        if (showSuggestions) {
          setSelectedSuggestion(prev => Math.max(0, prev - 1));
        } else {
          // Navigate command history
          if (historyIndex < history.length - 1) {
            const newIndex = historyIndex + 1;
            setHistoryIndex(newIndex);
            setValue(history[newIndex]);
          }
        }
        break;

      case 'ArrowDown':
        e.preventDefault();
        if (showSuggestions) {
          setSelectedSuggestion(prev => Math.min(filteredSuggestions.length - 1, prev + 1));
        } else {
          // Navigate command history
          if (historyIndex > 0) {
            const newIndex = historyIndex - 1;
            setHistoryIndex(newIndex);
            setValue(history[newIndex]);
          } else if (historyIndex === 0) {
            setHistoryIndex(-1);
            setValue('');
          }
        }
        break;

      case 'Escape':
        e.preventDefault();
        setShowSuggestions(false);
        setSelectedSuggestion(0);
        break;

      case 'Tab':
        e.preventDefault();
        if (showSuggestions && filteredSuggestions.length > 0) {
          const suggestion = filteredSuggestions[selectedSuggestion];
          setValue(suggestion.template);
          setShowSuggestions(false);
        }
        break;
    }
  };

  const handleExecute = async () => {
    if (!value.trim() || isExecuting) return;

    const parsed = parseCommand(value);
    if (!parsed.command) return;

    try {
      setIsExecuting(true);
      
      // Remove leading slash if present
      const command = parsed.command.startsWith('/') 
        ? parsed.command.substring(1) 
        : parsed.command;
      
      const args = parsed.arguments.join(' ');
      
      await onExecute(command, args);
      
      // Clear input and reset history index
      setValue('');
      setHistoryIndex(-1);
      setShowSuggestions(false);
    } catch (error) {
      console.error('Command execution failed:', error);
    } finally {
      setIsExecuting(false);
    }
  };

  const handleSuggestionClick = (suggestion: CommandSuggestion) => {
    setValue(suggestion.template);
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  return (
    <div className={`relative ${className}`}>
      {/* Input Container */}
      <div className="relative">
        <div className="flex items-center space-x-2 p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500">
          <CommandIcon className="w-4 h-4 text-gray-400 flex-shrink-0" />
          
          <input
            ref={inputRef}
            type="text"
            value={value}
            onChange={(e) => setValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled || isExecuting}
            className="flex-1 bg-transparent border-none outline-none text-gray-900 dark:text-white placeholder-gray-500 font-mono text-sm"
          />
          
          <button
            onClick={handleExecute}
            disabled={disabled || isExecuting || !value.trim()}
            className="p-1.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Execute command"
          >
            {isExecuting ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </button>
        </div>

        {/* Validation Messages */}
        {validation && (
          <div className="mt-2 space-y-1">
            {validation.errors.map((error, index) => (
              <div key={index} className="flex items-center space-x-2 text-sm text-red-600 dark:text-red-400">
                <AlertCircle className="w-3 h-3" />
                <span>{error}</span>
              </div>
            ))}
            {validation.warnings.map((warning, index) => (
              <div key={index} className="flex items-center space-x-2 text-sm text-yellow-600 dark:text-yellow-400">
                <AlertCircle className="w-3 h-3" />
                <span>{warning}</span>
              </div>
            ))}
            {validation.suggestions.map((suggestion, index) => (
              <div key={index} className="flex items-center space-x-2 text-sm text-blue-600 dark:text-blue-400">
                <CheckCircle className="w-3 h-3" />
                <span>{suggestion}</span>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Suggestions Dropdown */}
      {showSuggestions && (
        <div 
          ref={suggestionsRef}
          className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto"
        >
          {filteredSuggestions.map((suggestion, index) => (
            <div
              key={`${suggestion.name}-${index}`}
              className={`px-3 py-2 cursor-pointer transition-colors ${
                index === selectedSuggestion
                  ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                  : 'hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
              onClick={() => handleSuggestionClick(suggestion)}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-gray-900 dark:text-white">
                    {suggestion.name}
                  </div>
                  {suggestion.description && (
                    <div className="text-sm text-gray-500 dark:text-gray-400 truncate">
                      {suggestion.description}
                    </div>
                  )}
                  <div className="text-xs text-gray-400 dark:text-gray-500 font-mono">
                    {suggestion.template}
                  </div>
                </div>
                {(suggestion.agent || suggestion.model) && (
                  <div className="flex items-center space-x-1 ml-2">
                    {suggestion.agent && (
                      <span className="text-xs px-1.5 py-0.5 bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300 rounded">
                        {suggestion.agent}
                      </span>
                    )}
                    {suggestion.model && (
                      <span className="text-xs px-1.5 py-0.5 bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 rounded">
                        {suggestion.model}
                      </span>
                    )}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Help Text */}
      <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 flex items-center space-x-4">
        <span>↑↓ History</span>
        <span>Tab Complete</span>
        <span>Enter Execute</span>
        <span>Esc Cancel</span>
      </div>
    </div>
  );
}
