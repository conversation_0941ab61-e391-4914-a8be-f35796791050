'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { 
  Terminal, 
  X, 
  Minimize2, 
  Maximize2, 
  RotateCcw, 
  Copy, 
  Download,
  Settings,
  Plus,
  Trash2,
  Play,
  Square
} from 'lucide-react';
import { ShellSession, CommandHistoryItem } from '@/types/command';

interface ShellInterfaceProps {
  isOpen: boolean;
  onClose: () => void;
  onExecuteShell: (command: string, agent: string) => Promise<void>;
  sessionId?: string;
  currentAgent?: string;
  className?: string;
}

interface ShellOutput {
  id: string;
  type: 'input' | 'output' | 'error' | 'system';
  content: string;
  timestamp: Date;
  command?: string;
  exitCode?: number;
}

export function ShellInterface({
  isOpen,
  onClose,
  onExecuteShell,
  sessionId,
  currentAgent = 'general',
  className = ''
}: ShellInterfaceProps) {
  const [sessions, setSessions] = useState<ShellSession[]>([]);
  const [activeSessionId, setActiveSessionId] = useState<string | null>(null);
  const [input, setInput] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const [output, setOutput] = useState<ShellOutput[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const outputRef = useRef<HTMLDivElement>(null);
  const terminalRef = useRef<HTMLDivElement>(null);

  // Initialize with a default session
  useEffect(() => {
    if (isOpen && sessions.length === 0) {
      createNewSession();
    }
  }, [isOpen]);

  // Auto-scroll to bottom when new output is added
  useEffect(() => {
    if (outputRef.current) {
      outputRef.current.scrollTop = outputRef.current.scrollHeight;
    }
  }, [output]);

  // Focus input when opened
  useEffect(() => {
    if (isOpen && !isMinimized && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen, isMinimized]);

  const createNewSession = () => {
    const newSession: ShellSession = {
      id: Date.now().toString(),
      sessionId: sessionId || 'default',
      agent: currentAgent,
      history: [],
      isActive: true,
      workingDirectory: '~'
    };

    setSessions(prev => [...prev, newSession]);
    setActiveSessionId(newSession.id);
    
    // Add welcome message
    const welcomeOutput: ShellOutput = {
      id: Date.now().toString(),
      type: 'system',
      content: `Shell session started with agent: ${currentAgent}`,
      timestamp: new Date()
    };
    setOutput([welcomeOutput]);
  };

  const closeSession = (sessionId: string) => {
    setSessions(prev => prev.filter(s => s.id !== sessionId));
    if (activeSessionId === sessionId) {
      const remainingSessions = sessions.filter(s => s.id !== sessionId);
      setActiveSessionId(remainingSessions.length > 0 ? remainingSessions[0].id : null);
      if (remainingSessions.length === 0) {
        setOutput([]);
      }
    }
  };

  const switchSession = (sessionId: string) => {
    setActiveSessionId(sessionId);
    // Load session output (in a real implementation, this would be persisted)
    // For now, we'll just clear the output
    setOutput([]);
  };

  const getCurrentSession = () => {
    return sessions.find(s => s.id === activeSessionId);
  };

  const getCommandHistory = () => {
    const session = getCurrentSession();
    return session?.history.map(h => h.command) || [];
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (isExecuting) return;

    const history = getCommandHistory();

    switch (e.key) {
      case 'Enter':
        e.preventDefault();
        handleExecute();
        break;

      case 'ArrowUp':
        e.preventDefault();
        if (historyIndex < history.length - 1) {
          const newIndex = historyIndex + 1;
          setHistoryIndex(newIndex);
          setInput(history[history.length - 1 - newIndex]);
        }
        break;

      case 'ArrowDown':
        e.preventDefault();
        if (historyIndex > 0) {
          const newIndex = historyIndex - 1;
          setHistoryIndex(newIndex);
          setInput(history[history.length - 1 - newIndex]);
        } else if (historyIndex === 0) {
          setHistoryIndex(-1);
          setInput('');
        }
        break;

      case 'c':
        if (e.ctrlKey) {
          e.preventDefault();
          handleCancel();
        }
        break;

      case 'l':
        if (e.ctrlKey) {
          e.preventDefault();
          handleClear();
        }
        break;
    }
  };

  const handleExecute = async () => {
    if (!input.trim() || isExecuting) return;

    const command = input.trim();
    const session = getCurrentSession();
    if (!session) return;

    // Add input to output
    const inputOutput: ShellOutput = {
      id: Date.now().toString(),
      type: 'input',
      content: `${session.workingDirectory} $ ${command}`,
      timestamp: new Date(),
      command
    };
    setOutput(prev => [...prev, inputOutput]);

    // Add to history
    const historyItem: CommandHistoryItem = {
      id: Date.now().toString(),
      command,
      timestamp: new Date(),
      type: 'shell'
    };

    setSessions(prev => prev.map(s => 
      s.id === activeSessionId 
        ? { ...s, history: [...s.history, historyItem] }
        : s
    ));

    try {
      setIsExecuting(true);
      setInput('');
      setHistoryIndex(-1);

      // Handle built-in commands
      if (await handleBuiltInCommand(command)) {
        return;
      }

      // Execute shell command
      await onExecuteShell(command, session.agent);

      // Add success output (in a real implementation, this would come from the server)
      const successOutput: ShellOutput = {
        id: (Date.now() + 1).toString(),
        type: 'output',
        content: `Command executed: ${command}`,
        timestamp: new Date(),
        exitCode: 0
      };
      setOutput(prev => [...prev, successOutput]);

    } catch (error) {
      // Add error output
      const errorOutput: ShellOutput = {
        id: (Date.now() + 1).toString(),
        type: 'error',
        content: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        timestamp: new Date(),
        exitCode: 1
      };
      setOutput(prev => [...prev, errorOutput]);
    } finally {
      setIsExecuting(false);
    }
  };

  const handleBuiltInCommand = async (command: string): Promise<boolean> => {
    const [cmd, ...args] = command.split(' ');

    switch (cmd) {
      case 'clear':
        setOutput([]);
        return true;

      case 'history':
        const history = getCommandHistory();
        const historyOutput: ShellOutput = {
          id: Date.now().toString(),
          type: 'output',
          content: history.map((cmd, i) => `${i + 1}  ${cmd}`).join('\n'),
          timestamp: new Date()
        };
        setOutput(prev => [...prev, historyOutput]);
        return true;

      case 'pwd':
        const session = getCurrentSession();
        const pwdOutput: ShellOutput = {
          id: Date.now().toString(),
          type: 'output',
          content: session?.workingDirectory || '~',
          timestamp: new Date()
        };
        setOutput(prev => [...prev, pwdOutput]);
        return true;

      case 'exit':
        onClose();
        return true;

      default:
        return false;
    }
  };

  const handleCancel = () => {
    if (isExecuting) {
      setIsExecuting(false);
      const cancelOutput: ShellOutput = {
        id: Date.now().toString(),
        type: 'system',
        content: '^C',
        timestamp: new Date()
      };
      setOutput(prev => [...prev, cancelOutput]);
    }
  };

  const handleClear = () => {
    setOutput([]);
  };

  const handleCopy = () => {
    const text = output.map(o => o.content).join('\n');
    navigator.clipboard.writeText(text);
  };

  const handleDownload = () => {
    const text = output.map(o => 
      `[${o.timestamp.toISOString()}] ${o.type.toUpperCase()}: ${o.content}`
    ).join('\n');
    
    const blob = new Blob([text], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `shell-session-${Date.now()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (!isOpen) return null;

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 ${className}`}>
      <div 
        ref={terminalRef}
        className={`bg-gray-900 text-green-400 font-mono text-sm rounded-lg shadow-xl border border-gray-700 transition-all ${
          isFullscreen 
            ? 'w-full h-full rounded-none' 
            : isMinimized 
              ? 'w-96 h-12' 
              : 'w-4/5 h-4/5 max-w-6xl max-h-4xl'
        }`}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-3 border-b border-gray-700 bg-gray-800">
          <div className="flex items-center space-x-3">
            <Terminal className="w-4 h-4" />
            <span className="font-medium">Shell Interface</span>
            
            {/* Session Tabs */}
            <div className="flex items-center space-x-1 ml-4">
              {sessions.map((session, index) => (
                <button
                  key={session.id}
                  onClick={() => switchSession(session.id)}
                  className={`px-2 py-1 text-xs rounded transition-colors ${
                    session.id === activeSessionId
                      ? 'bg-gray-700 text-green-400'
                      : 'text-gray-400 hover:text-green-400'
                  }`}
                >
                  Shell {index + 1}
                  {sessions.length > 1 && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        closeSession(session.id);
                      }}
                      className="ml-1 text-gray-500 hover:text-red-400"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  )}
                </button>
              ))}
              <button
                onClick={createNewSession}
                className="p-1 text-gray-400 hover:text-green-400"
                title="New session"
              >
                <Plus className="w-3 h-3" />
              </button>
            </div>
          </div>

          <div className="flex items-center space-x-1">
            <button
              onClick={handleCopy}
              className="p-1 text-gray-400 hover:text-green-400"
              title="Copy output"
            >
              <Copy className="w-4 h-4" />
            </button>
            <button
              onClick={handleDownload}
              className="p-1 text-gray-400 hover:text-green-400"
              title="Download log"
            >
              <Download className="w-4 h-4" />
            </button>
            <button
              onClick={handleClear}
              className="p-1 text-gray-400 hover:text-green-400"
              title="Clear output"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
            <button
              onClick={() => setIsMinimized(!isMinimized)}
              className="p-1 text-gray-400 hover:text-green-400"
              title={isMinimized ? 'Restore' : 'Minimize'}
            >
              <Minimize2 className="w-4 h-4" />
            </button>
            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className="p-1 text-gray-400 hover:text-green-400"
              title={isFullscreen ? 'Exit fullscreen' : 'Fullscreen'}
            >
              <Maximize2 className="w-4 h-4" />
            </button>
            <button
              onClick={onClose}
              className="p-1 text-gray-400 hover:text-red-400"
              title="Close"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        {!isMinimized && (
          <>
            {/* Output Area */}
            <div 
              ref={outputRef}
              className="flex-1 p-4 overflow-y-auto bg-gray-900 min-h-0"
              style={{ height: 'calc(100% - 120px)' }}
            >
              {output.map((item) => (
                <div key={item.id} className="mb-1">
                  <span className={`${
                    item.type === 'input' ? 'text-white' :
                    item.type === 'error' ? 'text-red-400' :
                    item.type === 'system' ? 'text-yellow-400' :
                    'text-green-400'
                  }`}>
                    {item.content}
                  </span>
                </div>
              ))}
              {isExecuting && (
                <div className="text-yellow-400">
                  <span className="animate-pulse">Executing...</span>
                </div>
              )}
            </div>

            {/* Input Area */}
            <div className="p-4 border-t border-gray-700 bg-gray-800">
              <div className="flex items-center space-x-2">
                <span className="text-green-400 flex-shrink-0">
                  {getCurrentSession()?.workingDirectory || '~'} $
                </span>
                <input
                  ref={inputRef}
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  disabled={isExecuting}
                  className="flex-1 bg-transparent border-none outline-none text-green-400 placeholder-gray-500"
                  placeholder="Enter shell command..."
                />
                {isExecuting ? (
                  <button
                    onClick={handleCancel}
                    className="p-1 text-red-400 hover:text-red-300"
                    title="Cancel (Ctrl+C)"
                  >
                    <Square className="w-4 h-4" />
                  </button>
                ) : (
                  <button
                    onClick={handleExecute}
                    disabled={!input.trim()}
                    className="p-1 text-green-400 hover:text-green-300 disabled:opacity-50"
                    title="Execute"
                  >
                    <Play className="w-4 h-4" />
                  </button>
                )}
              </div>
              
              {/* Help Text */}
              <div className="mt-2 text-xs text-gray-500 flex items-center space-x-4">
                <span>↑↓ History</span>
                <span>Ctrl+C Cancel</span>
                <span>Ctrl+L Clear</span>
                <span>Enter Execute</span>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
