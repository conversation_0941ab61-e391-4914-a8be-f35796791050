/**
 * Command System Components
 * Export all command-related components for easy importing
 */

export { CommandPalette } from './command-palette';
export { CommandInput } from './command-input';
export { ShellInterface } from './shell-interface';

// Re-export types for convenience
export type {
  Command,
  CommandInput as CommandInputType,
  ShellInput,
  CommandResult,
  ShellResult,
  CommandHistoryItem,
  CommandSuggestion,
  ShellSession,
  CommandPaletteState,
  CommandInputState,
  ShellInterfaceState,
  ParsedCommand,
  BuiltInCommand,
  BuiltInCommandDefinition,
  CommandContext,
  CommandMode,
  CommandExecution,
  CommandKeyBinding,
  CommandValidationResult,
  AutocompleteResult,
} from '@/types/command';
