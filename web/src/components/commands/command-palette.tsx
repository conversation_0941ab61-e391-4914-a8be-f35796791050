'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { 
  Search, 
  Terminal, 
  Command as CommandIcon, 
  Clock, 
  ArrowRight, 
  X,
  Zap,
  <PERSON>ting<PERSON>,
  FileText,
  Users,
  Brain
} from 'lucide-react';
import { Command, CommandSuggestion, CommandHistoryItem, BuiltInCommand } from '@/types/command';
import { apiService } from '@/lib/api-service';

interface CommandPaletteProps {
  isOpen: boolean;
  onClose: () => void;
  onExecuteCommand: (command: string, args: string) => Promise<void>;
  onExecuteShell: (command: string) => Promise<void>;
  sessionId?: string;
  currentAgent?: string;
  currentModel?: string;
}

interface CommandCategory {
  name: string;
  icon: React.ReactNode;
  commands: CommandSuggestion[];
}

const BUILT_IN_COMMANDS: Record<BuiltInCommand, CommandSuggestion> = {
  help: { name: 'help', description: 'Show available commands', template: '/help' },
  clear: { name: 'clear', description: 'Clear the chat history', template: '/clear' },
  history: { name: 'history', description: 'Show command history', template: '/history' },
  exit: { name: 'exit', description: 'Exit the current session', template: '/exit' },
  init: { name: 'init', description: 'Initialize project with AGENTS.md', template: '/init' },
  status: { name: 'status', description: 'Show current status', template: '/status' },
  sessions: { name: 'sessions', description: 'List all sessions', template: '/sessions' },
  agents: { name: 'agents', description: 'List available agents', template: '/agents' },
  models: { name: 'models', description: 'List available models', template: '/models' },
  files: { name: 'files', description: 'Show file browser', template: '/files' },
  search: { name: 'search', description: 'Search in workspace', template: '/search {query}' },
};

export function CommandPalette({
  isOpen,
  onClose,
  onExecuteCommand,
  onExecuteShell,
  sessionId,
  currentAgent,
  currentModel
}: CommandPaletteProps) {
  const [query, setQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [mode, setMode] = useState<'command' | 'shell'>('command');
  const [suggestions, setSuggestions] = useState<CommandSuggestion[]>([]);
  const [history, setHistory] = useState<CommandHistoryItem[]>([]);
  const [customCommands, setCustomCommands] = useState<Command[]>([]);
  const [loading, setLoading] = useState(false);
  
  const inputRef = useRef<HTMLInputElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  // Load commands and history on mount
  useEffect(() => {
    if (isOpen) {
      loadCommands();
      loadHistory();
      // Focus input when opened
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isOpen]);

  // Update suggestions when query changes
  useEffect(() => {
    updateSuggestions();
  }, [query, mode, customCommands]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => Math.min(prev + 1, suggestions.length - 1));
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => Math.max(prev - 1, 0));
          break;
        case 'Enter':
          e.preventDefault();
          handleExecute();
          break;
        case 'Tab':
          e.preventDefault();
          setMode(prev => prev === 'command' ? 'shell' : 'command');
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, suggestions, selectedIndex]);

  // Scroll selected item into view
  useEffect(() => {
    if (listRef.current) {
      const selectedElement = listRef.current.children[selectedIndex] as HTMLElement;
      if (selectedElement) {
        selectedElement.scrollIntoView({ block: 'nearest' });
      }
    }
  }, [selectedIndex]);

  const loadCommands = async () => {
    try {
      setLoading(true);
      const response = await apiService.getCommands();
      if (response.success && response.data) {
        setCustomCommands(response.data);
      }
    } catch (error) {
      console.error('Failed to load commands:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadHistory = () => {
    // Load from localStorage or session storage
    const savedHistory = localStorage.getItem('command-history');
    if (savedHistory) {
      try {
        setHistory(JSON.parse(savedHistory));
      } catch (error) {
        console.error('Failed to parse command history:', error);
      }
    }
  };

  const saveHistory = (item: CommandHistoryItem) => {
    const newHistory = [item, ...history.slice(0, 49)]; // Keep last 50 items
    setHistory(newHistory);
    localStorage.setItem('command-history', JSON.stringify(newHistory));
  };

  const updateSuggestions = () => {
    let allSuggestions: CommandSuggestion[] = [];

    if (mode === 'command') {
      // Add built-in commands
      allSuggestions = Object.values(BUILT_IN_COMMANDS);
      
      // Add custom commands
      allSuggestions.push(...customCommands.map(cmd => ({
        name: cmd.name,
        description: cmd.description,
        template: cmd.template,
        agent: cmd.agent,
        model: cmd.model
      })));
    } else {
      // Shell mode - add common shell commands
      allSuggestions = [
        { name: 'ls', description: 'List directory contents', template: 'ls -la' },
        { name: 'pwd', description: 'Print working directory', template: 'pwd' },
        { name: 'cd', description: 'Change directory', template: 'cd {path}' },
        { name: 'cat', description: 'Display file contents', template: 'cat {file}' },
        { name: 'grep', description: 'Search text patterns', template: 'grep "{pattern}" {file}' },
        { name: 'find', description: 'Find files and directories', template: 'find . -name "{pattern}"' },
        { name: 'git', description: 'Git version control', template: 'git status' },
        { name: 'npm', description: 'Node package manager', template: 'npm install' },
        { name: 'python', description: 'Run Python script', template: 'python {script}' },
        { name: 'node', description: 'Run Node.js script', template: 'node {script}' },
      ];
    }

    // Filter by query
    if (query.trim()) {
      const lowerQuery = query.toLowerCase();
      allSuggestions = allSuggestions.filter(cmd => 
        cmd.name.toLowerCase().includes(lowerQuery) ||
        cmd.description?.toLowerCase().includes(lowerQuery) ||
        cmd.template.toLowerCase().includes(lowerQuery)
      );
      
      // Sort by relevance
      allSuggestions.sort((a, b) => {
        const aScore = getRelevanceScore(a, lowerQuery);
        const bScore = getRelevanceScore(b, lowerQuery);
        return bScore - aScore;
      });
    }

    setSuggestions(allSuggestions.slice(0, 10)); // Limit to 10 suggestions
    setSelectedIndex(0);
  };

  const getRelevanceScore = (suggestion: CommandSuggestion, query: string): number => {
    let score = 0;
    
    // Exact name match gets highest score
    if (suggestion.name.toLowerCase() === query) score += 100;
    else if (suggestion.name.toLowerCase().startsWith(query)) score += 50;
    else if (suggestion.name.toLowerCase().includes(query)) score += 25;
    
    // Description match
    if (suggestion.description?.toLowerCase().includes(query)) score += 10;
    
    // Template match
    if (suggestion.template.toLowerCase().includes(query)) score += 5;
    
    return score;
  };

  const handleExecute = async () => {
    const selectedSuggestion = suggestions[selectedIndex];
    const commandText = query.trim() || selectedSuggestion?.template || '';
    
    if (!commandText) return;

    try {
      setLoading(true);
      
      const historyItem: CommandHistoryItem = {
        id: Date.now().toString(),
        command: commandText,
        timestamp: new Date(),
        type: mode
      };

      if (mode === 'command') {
        // Parse command and arguments
        const [command, ...args] = commandText.split(' ');
        await onExecuteCommand(command, args.join(' '));
      } else {
        await onExecuteShell(commandText);
      }

      saveHistory(historyItem);
      onClose();
      setQuery('');
    } catch (error) {
      console.error('Command execution failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const getCategories = (): CommandCategory[] => {
    const categories: CommandCategory[] = [];
    
    if (mode === 'command') {
      categories.push({
        name: 'Built-in Commands',
        icon: <Zap className="w-4 h-4" />,
        commands: suggestions.filter(s => Object.keys(BUILT_IN_COMMANDS).includes(s.name))
      });
      
      categories.push({
        name: 'Custom Commands',
        icon: <Settings className="w-4 h-4" />,
        commands: suggestions.filter(s => !Object.keys(BUILT_IN_COMMANDS).includes(s.name))
      });
    } else {
      categories.push({
        name: 'Shell Commands',
        icon: <Terminal className="w-4 h-4" />,
        commands: suggestions
      });
    }
    
    return categories.filter(cat => cat.commands.length > 0);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-start justify-center pt-[10vh] bg-black bg-opacity-50">
      <div className="w-full max-w-2xl mx-4 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-1">
              <button
                onClick={() => setMode('command')}
                className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                  mode === 'command'
                    ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                    : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                }`}
              >
                <CommandIcon className="w-4 h-4 inline mr-1" />
                Commands
              </button>
              <button
                onClick={() => setMode('shell')}
                className={`px-3 py-1.5 text-sm rounded-md transition-colors ${
                  mode === 'shell'
                    ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300'
                    : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
                }`}
              >
                <Terminal className="w-4 h-4 inline mr-1" />
                Shell
              </button>
            </div>
          </div>
          
          <button
            onClick={onClose}
            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Search Input */}
        <div className="relative p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              ref={inputRef}
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              placeholder={mode === 'command' ? 'Search commands...' : 'Enter shell command...'}
              className="w-full pl-10 pr-4 py-3 text-lg bg-transparent border-none outline-none text-gray-900 dark:text-white placeholder-gray-500"
            />
          </div>
        </div>

        {/* Suggestions */}
        <div ref={listRef} className="max-h-96 overflow-y-auto">
          {loading ? (
            <div className="p-8 text-center text-gray-500">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto mb-2"></div>
              Loading commands...
            </div>
          ) : suggestions.length > 0 ? (
            getCategories().map((category, categoryIndex) => (
              <div key={category.name}>
                {getCategories().length > 1 && (
                  <div className="px-4 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 flex items-center space-x-2">
                    {category.icon}
                    <span>{category.name}</span>
                  </div>
                )}
                {category.commands.map((suggestion, index) => {
                  const globalIndex = getCategories()
                    .slice(0, categoryIndex)
                    .reduce((acc, cat) => acc + cat.commands.length, 0) + index;
                  
                  return (
                    <div
                      key={`${suggestion.name}-${index}`}
                      className={`px-4 py-3 cursor-pointer border-l-2 transition-colors ${
                        globalIndex === selectedIndex
                          ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-500'
                          : 'border-transparent hover:bg-gray-50 dark:hover:bg-gray-700'
                      }`}
                      onClick={() => {
                        setSelectedIndex(globalIndex);
                        handleExecute();
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-gray-900 dark:text-white">
                              {suggestion.name}
                            </span>
                            {suggestion.agent && (
                              <span className="text-xs px-2 py-0.5 bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300 rounded">
                                {suggestion.agent}
                              </span>
                            )}
                            {suggestion.model && (
                              <span className="text-xs px-2 py-0.5 bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 rounded">
                                {suggestion.model}
                              </span>
                            )}
                          </div>
                          {suggestion.description && (
                            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                              {suggestion.description}
                            </p>
                          )}
                          <p className="text-xs text-gray-400 dark:text-gray-500 mt-1 font-mono">
                            {suggestion.template}
                          </p>
                        </div>
                        <ArrowRight className="w-4 h-4 text-gray-400" />
                      </div>
                    </div>
                  );
                })}
              </div>
            ))
          ) : (
            <div className="p-8 text-center text-gray-500">
              {query ? 'No commands found' : 'Start typing to search commands'}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-4 py-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
            <div className="flex items-center space-x-4">
              <span>↑↓ Navigate</span>
              <span>↵ Execute</span>
              <span>Tab Switch Mode</span>
              <span>Esc Close</span>
            </div>
            {sessionId && (
              <div className="flex items-center space-x-2">
                {currentAgent && (
                  <span className="flex items-center space-x-1">
                    <Users className="w-3 h-3" />
                    <span>{currentAgent}</span>
                  </span>
                )}
                {currentModel && (
                  <span className="flex items-center space-x-1">
                    <Brain className="w-3 h-3" />
                    <span>{currentModel}</span>
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
