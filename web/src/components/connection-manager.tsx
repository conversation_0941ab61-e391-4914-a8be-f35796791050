'use client';

import { useState, useEffect } from 'react';
import { Plus, Trash2, Edit2, Check, X } from 'lucide-react';
import { ConnectionStatus } from './connection-status';

interface ServerConnection {
  id: string;
  name: string;
  url: string;
  lastConnected?: number;
  isActive?: boolean;
}

interface ConnectionManagerProps {
  onConnect: (url: string) => Promise<boolean>;
  currentConnection?: string;
  connectionStatus: 'disconnected' | 'connected' | 'testing';
  connectionError?: string | null;
}

export function ConnectionManager({ 
  onConnect, 
  currentConnection, 
  connectionStatus, 
  connectionError 
}: ConnectionManagerProps) {
  const [connections, setConnections] = useState<ServerConnection[]>([]);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newConnection, setNewConnection] = useState({ name: '', url: '' });

  // Load saved connections from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('opencode-connections');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        setConnections(parsed);
      } catch (error) {
        console.error('Failed to parse saved connections:', error);
      }
    } else {
      // Add default connection
      const defaultConnection: ServerConnection = {
        id: 'default',
        name: 'Local Server',
        url: 'http://127.0.0.1:40167',
        lastConnected: Date.now(),
        isActive: true
      };
      setConnections([defaultConnection]);
    }
  }, []);

  // Save connections to localStorage
  useEffect(() => {
    if (connections.length > 0) {
      localStorage.setItem('opencode-connections', JSON.stringify(connections));
    }
  }, [connections]);

  const handleConnect = async (connection: ServerConnection) => {
    const success = await onConnect(connection.url);
    if (success) {
      setConnections(prev => prev.map(conn => ({
        ...conn,
        isActive: conn.id === connection.id,
        lastConnected: conn.id === connection.id ? Date.now() : conn.lastConnected
      })));
    }
  };

  const handleAddNew = () => {
    if (newConnection.name && newConnection.url) {
      const connection: ServerConnection = {
        id: Date.now().toString(),
        name: newConnection.name,
        url: newConnection.url,
        isActive: false
      };
      setConnections(prev => [...prev, connection]);
      setNewConnection({ name: '', url: '' });
      setIsAddingNew(false);
    }
  };

  const handleDelete = (id: string) => {
    setConnections(prev => prev.filter(conn => conn.id !== id));
  };

  const handleEdit = (connection: ServerConnection) => {
    setEditingId(connection.id);
    setNewConnection({ name: connection.name, url: connection.url });
  };

  const handleSaveEdit = () => {
    if (editingId && newConnection.name && newConnection.url) {
      setConnections(prev => prev.map(conn => 
        conn.id === editingId 
          ? { ...conn, name: newConnection.name, url: newConnection.url }
          : conn
      ));
      setEditingId(null);
      setNewConnection({ name: '', url: '' });
    }
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setNewConnection({ name: '', url: '' });
    setIsAddingNew(false);
  };

  const formatLastConnected = (timestamp?: number) => {
    if (!timestamp) return 'Never';
    return new Date(timestamp).toLocaleString();
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          Server Connections
        </h3>
        <button
          onClick={() => setIsAddingNew(true)}
          className="flex items-center gap-2 px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          <Plus className="w-4 h-4" />
          Add Server
        </button>
      </div>

      <div className="space-y-2">
        {connections.map((connection) => (
          <div
            key={connection.id}
            className={`p-4 border rounded-lg ${
              connection.isActive 
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                : 'border-gray-200 dark:border-gray-700'
            }`}
          >
            {editingId === connection.id ? (
              <div className="space-y-3">
                <input
                  type="text"
                  value={newConnection.name}
                  onChange={(e) => setNewConnection(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Server name"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                />
                <input
                  type="url"
                  value={newConnection.url}
                  onChange={(e) => setNewConnection(prev => ({ ...prev, url: e.target.value }))}
                  placeholder="http://127.0.0.1:40167"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
                />
                <div className="flex gap-2">
                  <button
                    onClick={handleSaveEdit}
                    className="flex items-center gap-1 px-3 py-1 text-sm bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    <Check className="w-4 h-4" />
                    Save
                  </button>
                  <button
                    onClick={handleCancelEdit}
                    className="flex items-center gap-1 px-3 py-1 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700"
                  >
                    <X className="w-4 h-4" />
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {connection.name}
                    </h4>
                    {connection.isActive && (
                      <ConnectionStatus status={connectionStatus} error={connectionError} />
                    )}
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {connection.url}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-500">
                    Last connected: {formatLastConnected(connection.lastConnected)}
                  </p>
                </div>
                
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleConnect(connection)}
                    disabled={connectionStatus === 'testing'}
                    className={`px-3 py-1 text-sm rounded-md ${
                      connection.isActive
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : 'bg-blue-600 text-white hover:bg-blue-700'
                    } disabled:opacity-50`}
                  >
                    {connection.isActive ? 'Connected' : 'Connect'}
                  </button>
                  
                  <button
                    onClick={() => handleEdit(connection)}
                    className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                  >
                    <Edit2 className="w-4 h-4" />
                  </button>
                  
                  <button
                    onClick={() => handleDelete(connection.id)}
                    className="p-1 text-red-500 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
            )}
          </div>
        ))}

        {/* Add new connection form */}
        {isAddingNew && (
          <div className="p-4 border border-dashed border-gray-300 dark:border-gray-600 rounded-lg">
            <div className="space-y-3">
              <input
                type="text"
                value={newConnection.name}
                onChange={(e) => setNewConnection(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Server name (e.g., Local Development)"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
              />
              <input
                type="url"
                value={newConnection.url}
                onChange={(e) => setNewConnection(prev => ({ ...prev, url: e.target.value }))}
                placeholder="http://127.0.0.1:40167"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md"
              />
              <div className="flex gap-2">
                <button
                  onClick={handleAddNew}
                  className="flex items-center gap-1 px-3 py-1 text-sm bg-green-600 text-white rounded-md hover:bg-green-700"
                >
                  <Check className="w-4 h-4" />
                  Add
                </button>
                <button
                  onClick={handleCancelEdit}
                  className="flex items-center gap-1 px-3 py-1 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700"
                >
                  <X className="w-4 h-4" />
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
