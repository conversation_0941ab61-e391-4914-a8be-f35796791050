import { Settings, Menu, Folder } from 'lucide-react';
import { ConnectionStatus } from '@/components/connection-status';

interface HeaderProps {
  onMenuToggle?: () => void;
  connectionStatus?: 'disconnected' | 'connected' | 'testing';
  connectionError?: string | null;
  currentSession?: {
    id: string;
    title: string;
  } | null;
  onSettingsClick?: () => void;
  onFileManagerToggle?: () => void;
  isFileManagerOpen?: boolean;
}

export function Header({
  onMenuToggle,
  connectionStatus = 'disconnected',
  connectionError,
  currentSession,
  onSettingsClick,
  onFileManagerToggle,
  isFileManagerOpen = false
}: HeaderProps) {
  return (
    <header className="h-16 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between px-4">
      {/* Left side */}
      <div className="flex items-center gap-4">
        <button
          onClick={onMenuToggle}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md lg:hidden"
          aria-label="Toggle menu"
        >
          <Menu className="w-5 h-5" />
        </button>
        
        <div className="flex items-center gap-3">
          <h1 className="text-xl font-bold text-gray-900 dark:text-white">
            OpenCode
          </h1>
          
          {currentSession && (
            <>
              <span className="text-gray-400">/</span>
              <span className="text-gray-700 dark:text-gray-300 font-medium truncate max-w-xs">
                {currentSession.title}
              </span>
            </>
          )}
        </div>
      </div>

      {/* Center - Connection Status */}
      <div className="flex-1 flex justify-center">
        <ConnectionStatus status={connectionStatus} error={connectionError} />
      </div>



      {/* Right side */}
      <div className="flex items-center gap-2">
        <button
          onClick={onFileManagerToggle}
          className={`p-2 rounded-md transition-colors ${
            isFileManagerOpen
              ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
              : 'hover:bg-gray-100 dark:hover:bg-gray-800'
          }`}
          aria-label="Toggle file manager"
        >
          <Folder className="w-5 h-5" />
        </button>

        <button
          onClick={onSettingsClick}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md"
          aria-label="Settings"
        >
          <Settings className="w-5 h-5" />
        </button>
      </div>
    </header>
  );
}
