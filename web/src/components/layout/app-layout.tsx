'use client';

import { useState, useEffect, useCallback } from 'react';
import { Header } from './header';
import { Sidebar } from './sidebar';
import { SettingsModal } from '../settings-modal';
import { FileManager } from '../files/file-manager';
import { CommandPalette, ShellInterface } from '../commands';
import { apiService } from '@/lib/api-service';

interface AppLayoutProps {
  children: React.ReactNode;
  connectionStatus?: 'disconnected' | 'connected' | 'testing';
  connectionError?: string | null;
  sessions?: Array<{
    id: string;
    title: string;
    directory: string;
    version: string;
    projectID: string;
    time: {
      created: number;
      updated: number;
    };
  }>;
  currentSessionId?: string;
  onSessionSelect?: (sessionId: string) => void;
  onSessionCreate?: (title: string) => Promise<void>;
  onSessionDelete?: (sessionId: string) => Promise<void>;
  onSessionUpdate?: (sessionId: string, title: string) => Promise<void>;
  onConnect?: (url: string) => Promise<boolean>;
  onExecuteCommand?: (sessionId: string, command: string, args: string) => Promise<void>;
  onExecuteShell?: (sessionId: string, command: string, agent: string) => Promise<void>;
  currentConnection?: string;
  currentAgent?: string;
  currentModel?: string;
  isLoading?: boolean;
}

export function AppLayout({
  children,
  connectionStatus = 'disconnected',
  connectionError,
  sessions = [],
  currentSessionId,
  onSessionSelect,
  onSessionCreate,
  onSessionDelete,
  onSessionUpdate,
  onConnect,
  onExecuteCommand,
  onExecuteShell,
  currentConnection,
  currentAgent = 'general',
  currentModel,
  isLoading = false
}: AppLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [fileManagerOpen, setFileManagerOpen] = useState(false);
  const [commandPaletteOpen, setCommandPaletteOpen] = useState(false);
  const [shellInterfaceOpen, setShellInterfaceOpen] = useState(false);

  // Keyboard shortcuts for command system
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Command palette: Ctrl+K
      if (e.ctrlKey && e.key === 'k') {
        e.preventDefault();
        setCommandPaletteOpen(true);
      }

      // Shell interface: Ctrl+`
      if (e.ctrlKey && e.key === '`') {
        e.preventDefault();
        setShellInterfaceOpen(true);
      }

      // Close modals with Escape
      if (e.key === 'Escape') {
        setCommandPaletteOpen(false);
        setShellInterfaceOpen(false);
        setSettingsOpen(false);
        setFileManagerOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Command execution handlers
  const handleExecuteCommand = useCallback(async (command: string, args: string) => {
    if (!currentSessionId || !onExecuteCommand) return;

    try {
      await onExecuteCommand(currentSessionId, command, args);
    } catch (error) {
      console.error('Failed to execute command:', error);
      throw error;
    }
  }, [currentSessionId, onExecuteCommand]);

  const handleExecuteShell = useCallback(async (command: string) => {
    if (!currentSessionId || !onExecuteShell) return;

    try {
      await onExecuteShell(currentSessionId, command, currentAgent);
    } catch (error) {
      console.error('Failed to execute shell command:', error);
      throw error;
    }
  }, [currentSessionId, onExecuteShell, currentAgent]);

  const currentSession = sessions.find(s => s.id === currentSessionId);

  const handleMenuToggle = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleSidebarClose = () => {
    setSidebarOpen(false);
  };

  const handleSessionSelect = (sessionId: string) => {
    onSessionSelect?.(sessionId);
    // Close sidebar on mobile after selection
    if (window.innerWidth < 1024) {
      setSidebarOpen(false);
    }
  };

  return (
    <div className="h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <Header
        onMenuToggle={handleMenuToggle}
        connectionStatus={connectionStatus}
        connectionError={connectionError}
        currentSession={currentSession}
        onSettingsClick={() => setSettingsOpen(true)}
        onFileManagerToggle={() => setFileManagerOpen(!fileManagerOpen)}
        isFileManagerOpen={fileManagerOpen}
      />

      {/* Main content area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar */}
        <Sidebar
          isOpen={sidebarOpen}
          onClose={handleSidebarClose}
          sessions={sessions}
          currentSessionId={currentSessionId}
          onSessionSelect={handleSessionSelect}
          onSessionCreate={onSessionCreate}
          onSessionDelete={onSessionDelete}
          onSessionUpdate={onSessionUpdate}
          isLoading={isLoading}
        />

        {/* Main content */}
        <main className="flex-1 overflow-hidden bg-white dark:bg-gray-800 flex">
          {/* Chat Interface */}
          <div className={`${fileManagerOpen ? 'w-1/2' : 'flex-1'} transition-all duration-300`}>
            {children}
          </div>

          {/* File Manager */}
          {fileManagerOpen && (
            <div className="w-1/2 border-l border-gray-200 dark:border-gray-700">
              <FileManager
                isOpen={fileManagerOpen}
                onClose={() => setFileManagerOpen(false)}
              />
            </div>
          )}
        </main>
      </div>

      {/* Settings Modal */}
      <SettingsModal
        isOpen={settingsOpen}
        onClose={() => setSettingsOpen(false)}
        onConnect={onConnect || (() => Promise.resolve(false))}
        currentConnection={currentConnection}
        connectionStatus={connectionStatus}
        connectionError={connectionError}
      />

      {/* Command Palette */}
      <CommandPalette
        isOpen={commandPaletteOpen}
        onClose={() => setCommandPaletteOpen(false)}
        onExecuteCommand={handleExecuteCommand}
        onExecuteShell={handleExecuteShell}
        sessionId={currentSessionId}
        currentAgent={currentAgent}
        currentModel={currentModel}
      />

      {/* Shell Interface */}
      <ShellInterface
        isOpen={shellInterfaceOpen}
        onClose={() => setShellInterfaceOpen(false)}
        onExecuteShell={handleExecuteShell}
        sessionId={currentSessionId}
        currentAgent={currentAgent}
      />
    </div>
  );
}
