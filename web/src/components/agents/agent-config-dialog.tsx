'use client';

import React, { useState, useEffect } from 'react';
import { 
  X, 
  Save, 
  User, 
  Bot, 
  Shield, 
  Wrench, 
  Settings,
  Info,
  Plus,
  Minus,
  ChevronDown,
  ChevronRight
} from 'lucide-react';

interface Agent {
  name: string;
  description?: string;
  mode: 'subagent' | 'primary' | 'all';
  builtIn: boolean;
  topP?: number;
  temperature?: number;
  permission: {
    edit: 'ask' | 'allow' | 'deny';
    bash: Record<string, 'ask' | 'allow' | 'deny'>;
    webfetch: 'ask' | 'allow' | 'deny';
  };
  model?: {
    modelID: string;
    providerID: string;
  };
  prompt?: string;
  tools: Record<string, boolean>;
  options: Record<string, any>;
}

interface AgentConfigDialogProps {
  agent: Agent | null;
  isOpen: boolean;
  onClose: () => void;
  onSave?: (agent: Agent) => void;
  readOnly?: boolean;
}

export function AgentConfigDialog({ 
  agent, 
  isOpen, 
  onClose, 
  onSave,
  readOnly = false 
}: AgentConfigDialogProps) {
  const [formData, setFormData] = useState<Agent | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    basic: true,
    model: false,
    permissions: false,
    tools: false,
    prompt: false
  });

  useEffect(() => {
    if (agent) {
      setFormData({ ...agent });
      setErrors({});
    }
  }, [agent]);

  if (!isOpen || !agent || !formData) return null;

  const handleSave = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Agent name is required';
    }

    if (formData.temperature !== undefined && (formData.temperature < 0 || formData.temperature > 2)) {
      newErrors.temperature = 'Temperature must be between 0 and 2';
    }

    if (formData.topP !== undefined && (formData.topP < 0 || formData.topP > 1)) {
      newErrors.topP = 'Top P must be between 0 and 1';
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      onSave?.(formData);
      onClose();
    }
  };

  const updateFormData = (updates: Partial<Agent>) => {
    setFormData(prev => prev ? { ...prev, ...updates } : null);
  };

  const updatePermission = (key: keyof Agent['permission'], value: any) => {
    setFormData(prev => prev ? {
      ...prev,
      permission: {
        ...prev.permission,
        [key]: value
      }
    } : null);
  };

  const updateTool = (toolName: string, enabled: boolean) => {
    setFormData(prev => prev ? {
      ...prev,
      tools: {
        ...prev.tools,
        [toolName]: enabled
      }
    } : null);
  };

  const addBashPermission = () => {
    const pattern = prompt('Enter bash command pattern (e.g., "git", "*" for all):');
    if (pattern) {
      updatePermission('bash', {
        ...formData.permission.bash,
        [pattern]: 'ask'
      });
    }
  };

  const removeBashPermission = (pattern: string) => {
    const newBash = { ...formData.permission.bash };
    delete newBash[pattern];
    updatePermission('bash', newBash);
  };

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const SectionHeader = ({ title, icon: Icon, section, count }: { 
    title: string; 
    icon: any; 
    section: string; 
    count?: number;
  }) => (
    <button
      onClick={() => toggleSection(section)}
      className="w-full flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors"
    >
      <div className="flex items-center space-x-2">
        <Icon className="w-4 h-4 text-gray-600 dark:text-gray-400" />
        <span className="text-sm font-medium text-gray-900 dark:text-white">{title}</span>
        {count !== undefined && (
          <span className="text-xs text-gray-500 dark:text-gray-400 bg-gray-200 dark:bg-gray-600 px-2 py-0.5 rounded-full">
            {count}
          </span>
        )}
      </div>
      {expandedSections[section] ? (
        <ChevronDown className="w-4 h-4 text-gray-400" />
      ) : (
        <ChevronRight className="w-4 h-4 text-gray-400" />
      )}
    </button>
  );

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      <div 
        className="fixed inset-0"
        onClick={onClose}
      />
      <div className="relative w-full max-w-4xl max-h-[90vh] overflow-hidden bg-white dark:bg-gray-800 shadow-xl rounded-lg">
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              {formData.builtIn ? <Bot className="w-6 h-6 text-blue-600" /> : <User className="w-6 h-6 text-orange-600" />}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {readOnly ? 'Agent Details' : 'Configure Agent'}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {formData.builtIn ? 'Built-in agent' : 'Custom agent'} • {formData.mode}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          <div className="flex-1 overflow-y-auto p-6">
            <div className="space-y-4">
              <div className="space-y-3">
                <SectionHeader title="Basic Information" icon={Info} section="basic" />
                {expandedSections.basic && (
                  <div className="space-y-4 px-4 pb-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Name</label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => updateFormData({ name: e.target.value })}
                        disabled={readOnly || formData.builtIn}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:opacity-50"
                      />
                      {errors.name && <p className="text-sm text-red-600 mt-1">{errors.name}</p>}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description</label>
                      <textarea
                        value={formData.description || ''}
                        onChange={(e) => updateFormData({ description: e.target.value })}
                        disabled={readOnly}
                        rows={2}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:opacity-50"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Mode</label>
                      <select
                        value={formData.mode}
                        onChange={(e) => updateFormData({ mode: e.target.value as Agent['mode'] })}
                        disabled={readOnly || formData.builtIn}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:opacity-50"
                      >
                        <option value="primary">Primary</option>
                        <option value="subagent">Subagent</option>
                        <option value="all">All</option>
                      </select>
                    </div>
                  </div>
                )}
              </div>

              {/* Model Parameters */}
              <div className="space-y-3">
                <SectionHeader title="Model Parameters" icon={Settings} section="model" />
                {expandedSections.model && (
                  <div className="space-y-4 px-4 pb-2">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Temperature</label>
                        <input
                          type="number"
                          min="0"
                          max="2"
                          step="0.1"
                          value={formData.temperature || ''}
                          onChange={(e) => updateFormData({ temperature: parseFloat(e.target.value) || undefined })}
                          disabled={readOnly}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:opacity-50"
                        />
                        {errors.temperature && <p className="text-sm text-red-600 mt-1">{errors.temperature}</p>}
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Top P</label>
                        <input
                          type="number"
                          min="0"
                          max="1"
                          step="0.1"
                          value={formData.topP || ''}
                          onChange={(e) => updateFormData({ topP: parseFloat(e.target.value) || undefined })}
                          disabled={readOnly}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:opacity-50"
                        />
                        {errors.topP && <p className="text-sm text-red-600 mt-1">{errors.topP}</p>}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Permissions */}
              <div className="space-y-3">
                <SectionHeader
                  title="Permissions"
                  icon={Shield}
                  section="permissions"
                  count={Object.keys(formData.permission.bash).length + 2}
                />
                {expandedSections.permissions && (
                  <div className="space-y-4 px-4 pb-2">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">File Editing</label>
                        <select
                          value={formData.permission.edit}
                          onChange={(e) => updatePermission('edit', e.target.value)}
                          disabled={readOnly}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:opacity-50"
                        >
                          <option value="allow">Allow</option>
                          <option value="ask">Ask</option>
                          <option value="deny">Deny</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Web Fetch</label>
                        <select
                          value={formData.permission.webfetch}
                          onChange={(e) => updatePermission('webfetch', e.target.value)}
                          disabled={readOnly}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:opacity-50"
                        >
                          <option value="allow">Allow</option>
                          <option value="ask">Ask</option>
                          <option value="deny">Deny</option>
                        </select>
                      </div>
                    </div>
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">Bash Commands</label>
                        {!readOnly && (
                          <button
                            onClick={addBashPermission}
                            className="text-sm text-blue-600 hover:text-blue-700 flex items-center space-x-1"
                          >
                            <Plus className="w-3 h-3" />
                            <span>Add</span>
                          </button>
                        )}
                      </div>
                      <div className="space-y-2 max-h-32 overflow-y-auto">
                        {Object.entries(formData.permission.bash).map(([pattern, permission]) => (
                          <div key={pattern} className="flex items-center space-x-2">
                            <code className="flex-1 px-2 py-1 text-sm bg-gray-100 dark:bg-gray-700 rounded">{pattern}</code>
                            <select
                              value={permission}
                              onChange={(e) => updatePermission('bash', {
                                ...formData.permission.bash,
                                [pattern]: e.target.value
                              })}
                              disabled={readOnly}
                              className="px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:opacity-50"
                            >
                              <option value="allow">Allow</option>
                              <option value="ask">Ask</option>
                              <option value="deny">Deny</option>
                            </select>
                            {!readOnly && (
                              <button
                                onClick={() => removeBashPermission(pattern)}
                                className="text-red-600 hover:text-red-700"
                              >
                                <Minus className="w-3 h-3" />
                              </button>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Tools */}
              <div className="space-y-3">
                <SectionHeader
                  title="Tools"
                  icon={Wrench}
                  section="tools"
                  count={Object.values(formData.tools).filter(Boolean).length}
                />
                {expandedSections.tools && (
                  <div className="space-y-4 px-4 pb-2">
                    <div className="grid grid-cols-2 gap-2">
                      {Object.entries(formData.tools).map(([toolName, enabled]) => (
                        <label key={toolName} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={enabled}
                            onChange={(e) => updateTool(toolName, e.target.checked)}
                            disabled={readOnly}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 disabled:opacity-50"
                          />
                          <span className="text-sm text-gray-700 dark:text-gray-300">{toolName}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Custom Prompt */}
              {formData.prompt && (
                <div className="space-y-3">
                  <SectionHeader title="Custom Prompt" icon={Info} section="prompt" />
                  {expandedSections.prompt && (
                    <div className="px-4 pb-2">
                      <textarea
                        value={formData.prompt}
                        onChange={(e) => updateFormData({ prompt: e.target.value })}
                        disabled={readOnly}
                        rows={4}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white disabled:opacity-50 font-mono text-sm"
                      />
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {readOnly ? 'Close' : 'Cancel'}
            </button>
            {!readOnly && (
              <button
                onClick={handleSave}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center space-x-2"
              >
                <Save className="w-4 h-4" />
                <span>Save Changes</span>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
