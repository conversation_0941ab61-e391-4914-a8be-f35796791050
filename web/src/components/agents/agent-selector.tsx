'use client';

import React, { useState, useEffect } from 'react';
import {
  <PERSON>r,
  <PERSON><PERSON>,
  Set<PERSON>s,
  ChevronDown,
  Check,
  Shield,
  Zap,
  AlertCircle
} from 'lucide-react';
import { apiService } from '@/lib/api-service';

interface Agent {
  name: string;
  description?: string;
  mode: 'subagent' | 'primary' | 'all';
  builtIn: boolean;
  topP?: number;
  temperature?: number;
  permission: {
    edit: 'ask' | 'allow' | 'deny';
    bash: Record<string, 'ask' | 'allow' | 'deny'>;
    webfetch: 'ask' | 'allow' | 'deny';
  };
  model?: {
    modelID: string;
    providerID: string;
  };
  prompt?: string;
  tools: Record<string, boolean>;
  options: Record<string, any>;
}

interface AgentSelectorProps {
  selectedAgent?: string;
  onAgentSelect?: (agentName: string) => void;
  onAgentConfigure?: (agent: Agent) => void;
  className?: string;
}

export function AgentSelector({ 
  selectedAgent, 
  onAgentSelect, 
  onAgentConfigure,
  className = '' 
}: AgentSelectorProps) {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    loadAgents();
  }, []);

  const loadAgents = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.getAgents();
      if (response.success) {
        setAgents(response.data || []);
      } else {
        throw new Error(response.error || 'Failed to load agents');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load agents');
      console.error('Failed to load agents:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleAgentSelect = (agentName: string) => {
    onAgentSelect?.(agentName);
    setIsOpen(false);
  };

  const getAgentIcon = (agent: Agent) => {
    if (agent.builtIn) {
      switch (agent.mode) {
        case 'primary':
          return <Bot className="w-4 h-4 text-blue-600" />;
        case 'subagent':
          return <User className="w-4 h-4 text-green-600" />;
        default:
          return <Zap className="w-4 h-4 text-purple-600" />;
      }
    }
    return <User className="w-4 h-4 text-orange-600" />;
  };

  const getPermissionIcon = (permission: Agent['permission']) => {
    const hasRestrictive = 
      permission.edit === 'deny' || 
      permission.webfetch === 'deny' ||
      Object.values(permission.bash).some(p => p === 'deny');
    
    const hasAsk = 
      permission.edit === 'ask' || 
      permission.webfetch === 'ask' ||
      Object.values(permission.bash).some(p => p === 'ask');

    if (hasRestrictive) {
      return <Shield className="w-3 h-3 text-red-500" />;
    } else if (hasAsk) {
      return <Shield className="w-3 h-3 text-yellow-500" />;
    } else {
      return <Shield className="w-3 h-3 text-green-500" />;
    }
  };

  const selectedAgentData = agents.find(a => a.name === selectedAgent);

  return (
    <div className={`relative ${className}`}>
      {/* Selector Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={loading}
        className="w-full flex items-center justify-between px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
      >
        <div className="flex items-center space-x-2 min-w-0">
          {selectedAgentData ? (
            <>
              {getAgentIcon(selectedAgentData)}
              <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
                {selectedAgentData.name}
              </span>
              {getPermissionIcon(selectedAgentData.permission)}
            </>
          ) : (
            <>
              <Bot className="w-4 h-4 text-gray-400" />
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {loading ? 'Loading...' : 'Select agent'}
              </span>
            </>
          )}
        </div>
        <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-80 overflow-y-auto min-w-80">
          {error ? (
            <div className="p-3 text-sm text-red-600 dark:text-red-400 flex items-center space-x-2">
              <AlertCircle className="w-4 h-4" />
              <span>{error}</span>
            </div>
          ) : loading ? (
            <div className="p-3 text-sm text-gray-500 dark:text-gray-400">
              Loading agents...
            </div>
          ) : agents.length === 0 ? (
            <div className="p-3 text-sm text-gray-500 dark:text-gray-400">
              No agents available
            </div>
          ) : (
            agents.map((agent) => (
              <div
                key={agent.name}
                className="group relative"
              >
                <button
                  onClick={() => handleAgentSelect(agent.name)}
                  className="w-full px-3 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:bg-gray-50 dark:focus:bg-gray-700 border-b border-gray-100 dark:border-gray-700 last:border-b-0"
                >
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      {getAgentIcon(agent)}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2 min-w-0">
                          <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
                            {agent.name}
                          </span>
                          <span className="text-xs text-gray-500 dark:text-gray-400 capitalize flex-shrink-0">
                            {agent.mode}
                          </span>
                          {agent.builtIn && (
                            <span className="text-xs px-1.5 py-0.5 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full flex-shrink-0">
                              Built-in
                            </span>
                          )}
                        </div>

                        <div className="flex items-center space-x-1 flex-shrink-0">
                          {selectedAgent === agent.name && (
                            <Check className="w-4 h-4 text-blue-600" />
                          )}

                          {onAgentConfigure && (
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                onAgentConfigure(agent);
                              }}
                              className="p-1 opacity-0 group-hover:opacity-100 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-opacity"
                              title="Configure agent"
                            >
                              <Settings className="w-3 h-3" />
                            </button>
                          )}
                        </div>
                      </div>

                      {/* Compact description */}
                      {agent.description && (
                        <p className="text-xs text-gray-500 dark:text-gray-400 truncate mt-1">
                          {agent.description}
                        </p>
                      )}
                    </div>
                  </div>
                </button>
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
}
