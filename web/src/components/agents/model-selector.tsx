'use client';

import React, { useState, useEffect } from 'react';
import {
  Brain,
  ChevronDown,
  Check,
  Zap,
  Image,
  AlertCircle,
  Sparkles
} from 'lucide-react';
import { apiService } from '@/lib/api-service';

interface Model {
  id: string;
  name: string;
  attachment: boolean;
  reasoning: boolean;
  temperature: boolean;
  tool_call: boolean;
  knowledge?: string;
  release_date: string;
  last_updated: string;
  modalities: {
    input: string[];
    output: string[];
  };
  open_weights: boolean;
  cost: {
    input: number;
    output: number;
    cache_read?: number;
    cache_write?: number;
    reasoning?: number;
  };
  limit: {
    context: number;
    output: number;
  };
  experimental?: boolean;
}

interface Provider {
  id: string;
  name: string;
  env: string[];
  npm: string;
  api?: string;
  models: Record<string, Model>;
}

interface ProvidersResponse {
  providers: Provider[];
  default: Record<string, string>;
}

interface ModelSelectorProps {
  selectedModel?: { providerID: string; modelID: string };
  onModelSelect?: (providerID: string, modelID: string) => void;
  className?: string;
}

export function ModelSelector({ 
  selectedModel, 
  onModelSelect,
  className = '' 
}: ModelSelectorProps) {
  const [providers, setProviders] = useState<Provider[]>([]);
  const [defaultModels, setDefaultModels] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    loadProviders();
  }, []);

  const loadProviders = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.getProviders();
      if (response.success) {
        const data = response.data as ProvidersResponse;
        setProviders(data.providers || []);
        setDefaultModels(data.default || {});
      } else {
        throw new Error(response.error || 'Failed to load providers');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load providers');
      console.error('Failed to load providers:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleModelSelect = (providerID: string, modelID: string) => {
    onModelSelect?.(providerID, modelID);
    setIsOpen(false);
  };

  const getModelIcon = (model: Model) => {
    if (model.reasoning) {
      return <Sparkles className="w-4 h-4 text-purple-600" />;
    } else if (model.attachment) {
      return <Image className="w-4 h-4 text-blue-600" />;
    } else {
      return <Brain className="w-4 h-4 text-gray-600" />;
    }
  };

  const formatCost = (cost: number) => {
    if (cost === 0) return 'Free';
    if (cost < 1) return `$${cost.toFixed(3)}`;
    return `$${cost.toFixed(2)}`;
  };

  const formatContext = (tokens: number) => {
    if (tokens >= 1000000) return `${(tokens / 1000000).toFixed(1)}M`;
    if (tokens >= 1000) return `${(tokens / 1000).toFixed(0)}K`;
    return tokens.toString();
  };

  const getSelectedModelData = () => {
    if (!selectedModel) return null;
    const provider = providers.find(p => p.id === selectedModel.providerID);
    if (!provider) return null;
    const model = provider.models[selectedModel.modelID];
    return model ? { provider, model } : null;
  };

  const getAllModels = () => {
    const allModels: Array<{ provider: Provider; model: Model; modelId: string }> = [];
    providers.forEach(provider => {
      Object.entries(provider.models).forEach(([modelId, model]) => {
        allModels.push({ provider, model, modelId });
      });
    });
    return allModels;
  };

  const filteredModels = getAllModels().filter(({ provider, model, modelId }) => {
    const searchLower = searchQuery.toLowerCase();
    return (
      model.name.toLowerCase().includes(searchLower) ||
      modelId.toLowerCase().includes(searchLower) ||
      provider.name.toLowerCase().includes(searchLower)
    );
  });

  const selectedModelData = getSelectedModelData();

  return (
    <div className={`relative ${className}`}>
      {/* Selector Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={loading}
        className="w-full flex items-center justify-between px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
      >
        <div className="flex items-center space-x-2 min-w-0">
          {selectedModelData ? (
            <>
              {getModelIcon(selectedModelData.model)}
              <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
                {selectedModelData.model.name}
              </span>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                ({selectedModelData.provider.name})
              </span>
            </>
          ) : (
            <>
              <Brain className="w-4 h-4 text-gray-400" />
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {loading ? 'Loading...' : 'Select model'}
              </span>
            </>
          )}
        </div>
        <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-w-md">
          {/* Search */}
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <input
              type="text"
              placeholder="Search models..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Models List */}
          <div className="max-h-80 overflow-y-auto">
            {error ? (
              <div className="p-3 text-sm text-red-600 dark:text-red-400 flex items-center space-x-2">
                <AlertCircle className="w-4 h-4" />
                <span>{error}</span>
              </div>
            ) : loading ? (
              <div className="p-3 text-sm text-gray-500 dark:text-gray-400">
                Loading models...
              </div>
            ) : filteredModels.length === 0 ? (
              <div className="p-3 text-sm text-gray-500 dark:text-gray-400">
                {searchQuery ? 'No models match your search' : 'No models available'}
              </div>
            ) : (
              filteredModels.map(({ provider, model, modelId }) => (
                <button
                  key={`${provider.id}/${modelId}`}
                  onClick={() => handleModelSelect(provider.id, modelId)}
                  className="w-full px-3 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:bg-gray-50 dark:focus:bg-gray-700 border-b border-gray-100 dark:border-gray-700 last:border-b-0"
                >
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      {getModelIcon(model)}
                    </div>

                    <div className="flex-1 min-w-0">
                      {/* Model name and badges */}
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
                          {model.name}
                        </span>
                        <div className="flex items-center space-x-1 flex-shrink-0">
                          {model.cost.input === 0 && model.cost.output === 0 && (
                            <span className="text-xs px-1.5 py-0.5 bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400 rounded-full">
                              Free
                            </span>
                          )}
                          {selectedModel?.providerID === provider.id && selectedModel?.modelID === modelId && (
                            <Check className="w-4 h-4 text-blue-600" />
                          )}
                        </div>
                      </div>

                      {/* Provider and compact specs */}
                      <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                        <span className="font-medium truncate">{provider.name}</span>
                        <div className="flex items-center space-x-2 flex-shrink-0">
                          <span>{formatContext(model.limit.context)}</span>
                          <span>•</span>
                          <span>{formatCost(model.cost.input)}/{formatCost(model.cost.output)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </button>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
}
