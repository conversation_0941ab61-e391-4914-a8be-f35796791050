'use client';

import { useState } from 'react';
import { ConnectionStatus } from './connection-status';

interface ConnectionSetupProps {
  onConnect: (baseUrl: string) => Promise<boolean>;
  connectionStatus: 'disconnected' | 'connected' | 'testing';
  connectionError?: string | null;
  isLoading?: boolean;
}

export function ConnectionSetup({ 
  onConnect, 
  connectionStatus, 
  connectionError, 
  isLoading = false 
}: ConnectionSetupProps) {
  const [baseUrl, setBaseUrl] = useState('http://127.0.0.1:40167');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onConnect(baseUrl);
  };

  if (connectionStatus === 'connected') {
    return null; // Don't show setup when connected
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            OpenCode Web UI
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Connect to your OpenCode server to get started
          </p>
        </div>

        <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label 
                htmlFor="baseUrl" 
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
              >
                Server URL
              </label>
              <input
                id="baseUrl"
                type="url"
                value={baseUrl}
                onChange={(e) => setBaseUrl(e.target.value)}
                placeholder="http://127.0.0.1:40167"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                required
              />
              <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Enter the URL and port of your OpenCode server
              </p>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Connecting...' : 'Connect'}
            </button>

            <div className="flex justify-center">
              <ConnectionStatus status={connectionStatus} error={connectionError} />
            </div>
          </form>

          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Quick Start
            </h3>
            <div className="text-xs text-gray-500 dark:text-gray-400 space-y-2">
              <p>1. Start OpenCode in your project directory:</p>
              <code className="block bg-gray-100 dark:bg-gray-700 p-2 rounded text-xs">
                opencode serve
              </code>
              <p>2. Copy the server URL and port shown in the terminal</p>
              <p>3. Paste it above and click Connect</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
