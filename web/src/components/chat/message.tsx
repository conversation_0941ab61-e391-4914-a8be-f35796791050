'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON>r, <PERSON><PERSON>, <PERSON>, Zap } from 'lucide-react';
import {
  TextPart,
  ReasoningPart,
  ToolInvocationPart,
  FilePart,
  StepStartPart,
  StepFinishPart,
  SourceUrlPart
} from './message-parts';
import { MessageActions } from './message-actions';

interface BaseMessagePart {
  id: string;
  messageID: string;
  sessionID: string;
  synthetic?: boolean;
}

interface TextMessagePart extends BaseMessagePart {
  type: "text";
  text: string;
  time?: {
    start?: number;
    end?: number;
  };
}

interface ReasoningMessagePart extends BaseMessagePart {
  type: "reasoning";
  text: string;
}

interface ToolInvocationMessagePart extends BaseMessagePart {
  type: "tool-invocation";
  toolCallId: string;
  toolName: string;
  args: Record<string, any>;
  state: {
    status: "running" | "success" | "error";
    metadata?: Record<string, any>;
  };
  result?: {
    title: string;
    output: string;
    metadata: Record<string, any>;
  };
}

interface ToolMessagePart extends BaseMessagePart {
  type: "tool";
  tool: string;
  callID: string;
  state: {
    status: "completed" | "running" | "error";
    input: Record<string, any>;
    output: string;
    metadata: Record<string, any>;
    title: string;
    time: {
      start: number;
      end: number;
    };
  };
}

interface SourceUrlMessagePart extends BaseMessagePart {
  type: "source-url";
  url: string;
  title?: string;
}

interface FileMessagePart extends BaseMessagePart {
  type: "file";
  path: string;
  content: string;
  language?: string;
}

interface StepStartMessagePart extends BaseMessagePart {
  type: "step-start";
  step?: number;
}

interface StepFinishMessagePart extends BaseMessagePart {
  type: "step-finish";
  cost: number;
  tokens: {
    input: number;
    output: number;
    reasoning: number;
    cache: {
      read: number;
      write: number;
    };
  };
}

type MessagePart = TextMessagePart | ReasoningMessagePart | ToolInvocationMessagePart | ToolMessagePart | SourceUrlMessagePart | FileMessagePart | StepStartMessagePart | StepFinishMessagePart;

interface MessageInfo {
  id: string;
  role: 'user' | 'assistant';
  sessionID: string;
  time: {
    created: number;
    completed?: number;
  };
  tokens?: {
    input: number;
    output: number;
    reasoning: number;
    cache: {
      write: number;
      read: number;
    };
  };
  cost?: number;
  modelID?: string;
  providerID?: string;
}

export interface Message {
  info: MessageInfo;
  parts: MessagePart[];
  blocked?: boolean;
}

interface MessageProps {
  message: Message;
  isLast?: boolean;
  onRevert?: (messageId: string) => void;
  onFeedback?: (messageId: string, feedback: 'positive' | 'negative') => void;
}

export function Message({ message, isLast = false, onRevert, onFeedback }: MessageProps) {
  const [copiedPartId, setCopiedPartId] = useState<string | null>(null);

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const copyToClipboard = async (text: string, partId: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedPartId(partId);
      setTimeout(() => setCopiedPartId(null), 2000);
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  const renderMessagePart = (part: MessagePart) => {
    switch (part.type) {
      case 'text':
        return (
          <TextPart
            key={part.id}
            id={part.id}
            text={part.text}
            messageID={part.messageID}
            sessionID={part.sessionID}
            time={part.time}
          />
        );

      case 'reasoning':
        return (
          <ReasoningPart
            key={part.id}
            id={part.id}
            text={part.text}
            messageID={part.messageID}
            sessionID={part.sessionID}
          />
        );

      case 'tool-invocation':
        return (
          <ToolInvocationPart
            key={part.id}
            id={part.id}
            toolCallId={part.toolCallId}
            toolName={part.toolName}
            args={part.args}
            state={part.state}
            result={part.result}
            messageID={part.messageID}
            sessionID={part.sessionID}
          />
        );

      case 'tool':
        return (
          <ToolInvocationPart
            key={part.id}
            id={part.id}
            toolCallId={part.callID}
            toolName={part.tool}
            args={part.state.input}
            state={{
              status: part.state.status === 'completed' ? 'success' : part.state.status,
              metadata: part.state.metadata
            }}
            result={{
              title: part.state.title,
              output: part.state.output,
              metadata: part.state.metadata
            }}
            messageID={part.messageID}
            sessionID={part.sessionID}
          />
        );

      case 'source-url':
        return (
          <SourceUrlPart
            key={part.id}
            id={part.id}
            url={part.url}
            title={part.title}
            messageID={part.messageID}
            sessionID={part.sessionID}
          />
        );

      case 'file':
        return (
          <FilePart
            key={part.id}
            id={part.id}
            path={part.path}
            content={part.content}
            language={part.language}
            messageID={part.messageID}
            sessionID={part.sessionID}
          />
        );

      case 'step-start':
        return (
          <StepStartPart
            key={part.id}
            id={part.id}
            step={part.step}
            messageID={part.messageID}
            sessionID={part.sessionID}
          />
        );

      case 'step-finish':
        return (
          <StepFinishPart
            key={part.id}
            id={part.id}
            cost={part.cost}
            tokens={part.tokens}
            messageID={part.messageID}
            sessionID={part.sessionID}
          />
        );

      default:
        return (
          <div key={(part as any).id} className="text-xs text-gray-500 dark:text-gray-400 italic">
            Unknown part type: {(part as any).type}
          </div>
        );
    }
  };

  const isUser = message.info.role === 'user';
  const hasContent = message.parts.length > 0;

  // Extract text content for message actions
  const textContent = message.parts
    .filter(part => part.type === 'text')
    .map(part => (part as any).text)
    .join('\n');

  if (!hasContent) {
    return null;
  }

  return (
    <div className={`group flex gap-3 p-4 ${isUser ? 'bg-gray-50 dark:bg-gray-800' : 'bg-white dark:bg-gray-900'}`}>
      {/* Avatar */}
      <div className="flex-shrink-0">
        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
          isUser 
            ? 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400' 
            : 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400'
        }`}>
          {isUser ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        {/* Header */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm text-gray-900 dark:text-white">
              {isUser ? 'You' : 'OpenCode'}
            </span>
            <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center gap-1">
              <Clock className="w-3 h-3" />
              {formatTime(message.info.time.created)}
            </span>
            {message.info.modelID && (
              <span className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                {message.info.modelID}
              </span>
            )}
          </div>

          {/* Message Actions */}
          {!isUser && (
            <MessageActions
              messageId={message.info.id}
              sessionId={message.info.sessionID}
              messageText={textContent}
              onRevert={onRevert}
              onFeedback={onFeedback}
            />
          )}
        </div>

        {/* Message parts */}
        <div className="space-y-2">
          {message.parts.map(renderMessagePart)}
        </div>

        {/* Footer with metadata */}
        {message.info.tokens && (
          <div className="mt-3 pt-2 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
              <span>
                Tokens: {message.info.tokens.input + message.info.tokens.output}
              </span>
              {message.info.cost !== undefined && (
                <span>Cost: ${message.info.cost.toFixed(4)}</span>
              )}
              {message.info.time.completed && (
                <span>
                  Duration: {((message.info.time.completed - message.info.time.created) / 1000).toFixed(1)}s
                </span>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
