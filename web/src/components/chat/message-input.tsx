'use client';

import { useState, useRef, useEffect } from 'react';
import { Send, Loader2, Square, Command as CommandIcon } from 'lucide-react';
import { CommandSuggestion } from '@/types/command';
import { apiService } from '@/lib/api-service';

interface MessageInputProps {
  onSendMessage: (message: string) => Promise<void>;
  onExecuteCommand?: (command: string, args: string) => Promise<void>;
  isLoading?: boolean;
  onStop?: () => void;
  disabled?: boolean;
  placeholder?: string;
  sessionId?: string;
}

export function MessageInput({
  onSendMessage,
  onExecuteCommand,
  isLoading = false,
  onStop,
  disabled = false,
  placeholder = "Type your message...",
  sessionId
}: MessageInputProps) {
  const [message, setMessage] = useState('');
  const [showCommandSuggestions, setShowCommandSuggestions] = useState(false);
  const [commandSuggestions, setCommandSuggestions] = useState<CommandSuggestion[]>([]);
  const [selectedSuggestion, setSelectedSuggestion] = useState(0);
  const [isCommandMode, setIsCommandMode] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
    }
  }, [message]);

  // Check if message starts with / for command mode
  useEffect(() => {
    const isCommand = message.startsWith('/');
    setIsCommandMode(isCommand);

    if (isCommand && message.length > 1) {
      loadCommandSuggestions(message.substring(1));
    } else {
      setShowCommandSuggestions(false);
      setCommandSuggestions([]);
    }
  }, [message]);

  // Load command suggestions
  const loadCommandSuggestions = async (query: string) => {
    try {
      const response = await apiService.getCommands();
      if (response.success && response.data) {
        const filtered = response.data
          .filter(cmd =>
            cmd.name.toLowerCase().includes(query.toLowerCase()) ||
            cmd.description?.toLowerCase().includes(query.toLowerCase())
          )
          .map(cmd => ({
            name: cmd.name,
            description: cmd.description,
            template: `/${cmd.name}`,
            agent: cmd.agent,
            model: cmd.model
          }))
          .slice(0, 5);

        setCommandSuggestions(filtered);
        setShowCommandSuggestions(filtered.length > 0);
        setSelectedSuggestion(0);
      }
    } catch (error) {
      console.error('Failed to load command suggestions:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const trimmedMessage = message.trim();
    if (!trimmedMessage || isLoading || disabled) return;

    const messageToSend = trimmedMessage;
    setMessage('');
    setShowCommandSuggestions(false);

    try {
      // Check if it's a command
      if (messageToSend.startsWith('/') && onExecuteCommand) {
        const [command, ...args] = messageToSend.substring(1).split(' ');
        await onExecuteCommand(command, args.join(' '));
      } else {
        await onSendMessage(messageToSend);
      }
    } catch (error) {
      // Restore message on error
      setMessage(messageToSend);
      console.error('Failed to send message:', error);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (showCommandSuggestions) {
      switch (e.key) {
        case 'ArrowUp':
          e.preventDefault();
          setSelectedSuggestion(prev => Math.max(0, prev - 1));
          break;
        case 'ArrowDown':
          e.preventDefault();
          setSelectedSuggestion(prev => Math.min(commandSuggestions.length - 1, prev + 1));
          break;
        case 'Tab':
        case 'Enter':
          if (e.key === 'Tab' || (e.key === 'Enter' && !e.shiftKey)) {
            e.preventDefault();
            if (commandSuggestions[selectedSuggestion]) {
              setMessage(commandSuggestions[selectedSuggestion].template + ' ');
              setShowCommandSuggestions(false);
              textareaRef.current?.focus();
            }
          }
          break;
        case 'Escape':
          e.preventDefault();
          setShowCommandSuggestions(false);
          break;
      }
    } else if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };



  const handleStop = () => {
    if (onStop) {
      onStop();
    }
  };

  return (
    <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
      <form onSubmit={handleSubmit} className="p-4">
        <div className="flex gap-3 items-end">
          {/* Message input */}
          <div className="flex-1 relative">
            <div className="relative">
              {isCommandMode && (
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 z-10">
                  <CommandIcon className="w-4 h-4 text-blue-500" />
                </div>
              )}
              <textarea
                ref={textareaRef}
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={disabled ? "Select a session to start chatting..." : isCommandMode ? "Type command..." : placeholder}
                disabled={disabled || isLoading}
                className={`w-full resize-none rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 ${
                  isCommandMode ? 'pl-10' : 'pl-4'
                } py-3 pr-12 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed ${
                  isCommandMode ? 'border-blue-300 dark:border-blue-600' : ''
                }`}
                rows={1}
                style={{ minHeight: '48px', maxHeight: '200px' }}
              />
            </div>

            {/* Command Suggestions */}
            {showCommandSuggestions && (
              <div
                ref={suggestionsRef}
                className="absolute bottom-full left-0 right-0 mb-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto"
              >
                <div className="p-2 text-xs font-medium text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
                  Commands
                </div>
                {commandSuggestions.map((suggestion, index) => (
                  <div
                    key={`${suggestion.name}-${index}`}
                    className={`px-3 py-2 cursor-pointer transition-colors ${
                      index === selectedSuggestion
                        ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                        : 'hover:bg-gray-50 dark:hover:bg-gray-700'
                    }`}
                    onClick={() => {
                      setMessage(suggestion.template + ' ');
                      setShowCommandSuggestions(false);
                      textareaRef.current?.focus();
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-gray-900 dark:text-white">
                          {suggestion.name}
                        </div>
                        {suggestion.description && (
                          <div className="text-sm text-gray-500 dark:text-gray-400 truncate">
                            {suggestion.description}
                          </div>
                        )}
                      </div>
                      {(suggestion.agent || suggestion.model) && (
                        <div className="flex items-center space-x-1 ml-2">
                          {suggestion.agent && (
                            <span className="text-xs px-1.5 py-0.5 bg-purple-100 dark:bg-purple-900 text-purple-700 dark:text-purple-300 rounded">
                              {suggestion.agent}
                            </span>
                          )}
                          {suggestion.model && (
                            <span className="text-xs px-1.5 py-0.5 bg-green-100 dark:bg-green-900 text-green-700 dark:text-green-300 rounded">
                              {suggestion.model}
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Character count */}
            {message.length > 0 && (
              <div className="absolute bottom-1 right-1 text-xs text-gray-400">
                {message.length}
              </div>
            )}
          </div>

          {/* Send/Stop button */}
          <div className="flex gap-2">
            {isLoading ? (
              <button
                type="button"
                onClick={handleStop}
                className="flex items-center justify-center w-12 h-12 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
                aria-label="Stop generation"
              >
                <Square className="w-4 h-4" />
              </button>
            ) : (
              <button
                type="submit"
                disabled={!message.trim() || disabled}
                className="flex items-center justify-center w-12 h-12 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
                aria-label="Send message"
              >
                <Send className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>

        {/* Helper text */}
        <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 flex items-center justify-between">
          <div>
            {isCommandMode ? (
              <span>
                Type <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs">/</kbd> for commands •
                <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs ml-1">↑↓</kbd> navigate •
                <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs ml-1">Tab</kbd> complete
              </span>
            ) : (
              <span>
                Press <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs">Enter</kbd> to send,
                <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs ml-1">Shift + Enter</kbd> for new line
              </span>
            )}
          </div>
          {isCommandMode && (
            <div className="text-blue-600 dark:text-blue-400">
              Command Mode
            </div>
          )}
        </div>
      </form>
    </div>
  );
}
