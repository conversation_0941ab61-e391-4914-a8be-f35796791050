'use client';

import { useState } from 'react';
import { Co<PERSON>, Check, RotateCcw, ThumbsUp, ThumbsDown, MoreHorizontal } from 'lucide-react';

interface MessageActionsProps {
  messageId: string;
  sessionId: string;
  messageText?: string;
  onRevert?: (messageId: string) => void;
  onFeedback?: (messageId: string, feedback: 'positive' | 'negative') => void;
}

export function MessageActions({ 
  messageId, 
  sessionId, 
  messageText, 
  onRevert, 
  onFeedback 
}: MessageActionsProps) {
  const [copied, setCopied] = useState(false);
  const [feedback, setFeedback] = useState<'positive' | 'negative' | null>(null);
  const [showMore, setShowMore] = useState(false);

  const copyMessage = async () => {
    if (!messageText) return;
    
    try {
      await navigator.clipboard.writeText(messageText);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  };

  const handleFeedback = (type: 'positive' | 'negative') => {
    setFeedback(type);
    onFeedback?.(messageId, type);
  };

  const handleRevert = () => {
    onRevert?.(messageId);
  };

  return (
    <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
      {/* Copy button */}
      {messageText && (
        <button
          onClick={copyMessage}
          className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          aria-label="Copy message"
        >
          {copied ? (
            <Check className="w-3 h-3 text-green-600" />
          ) : (
            <Copy className="w-3 h-3" />
          )}
        </button>
      )}

      {/* Feedback buttons */}
      <button
        onClick={() => handleFeedback('positive')}
        className={`p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded ${
          feedback === 'positive' 
            ? 'text-green-600 dark:text-green-400' 
            : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
        }`}
        aria-label="Good response"
      >
        <ThumbsUp className="w-3 h-3" />
      </button>

      <button
        onClick={() => handleFeedback('negative')}
        className={`p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded ${
          feedback === 'negative' 
            ? 'text-red-600 dark:text-red-400' 
            : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
        }`}
        aria-label="Poor response"
      >
        <ThumbsDown className="w-3 h-3" />
      </button>

      {/* More actions */}
      <div className="relative">
        <button
          onClick={() => setShowMore(!showMore)}
          className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          aria-label="More actions"
        >
          <MoreHorizontal className="w-3 h-3" />
        </button>

        {showMore && (
          <div className="absolute right-0 top-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg py-1 z-10 min-w-[120px]">
            {onRevert && (
              <button
                onClick={handleRevert}
                className="w-full px-3 py-1.5 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
              >
                <RotateCcw className="w-3 h-3" />
                Revert
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
