'use client';

import { useState, useEffect, useRef } from 'react';
import { Message } from './message';
import { MessageInput } from './message-input';
import { ChatControls } from './chat-controls';
import { MessageSquare, RefreshCw } from 'lucide-react';

// Using Message interface from message.tsx

interface ChatInterfaceProps {
  sessionId?: string;
  onSendMessage: (sessionId: string, message: string) => Promise<void>;
  onExecuteCommand?: (sessionId: string, command: string, args: string) => Promise<void>;
  onLoadMessages: (sessionId: string) => Promise<Message[]>;
  isLoading?: boolean;
  onStop?: () => void;
  connectionStatus?: 'disconnected' | 'connected' | 'testing';
}

export function ChatInterface({
  sessionId,
  onSendMessage,
  onExecuteCommand,
  onLoadMessages,
  isLoading = false,
  onStop,
  connectionStatus = 'disconnected'
}: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loadingMessages, setLoadingMessages] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Load messages when session changes
  useEffect(() => {
    if (sessionId) {
      loadMessages();
    } else {
      setMessages([]);
      setError(null);
    }
  }, [sessionId]);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const loadMessages = async () => {
    if (!sessionId) return;

    setLoadingMessages(true);
    setError(null);

    try {
      const loadedMessages = await onLoadMessages(sessionId);
      setMessages(loadedMessages);
    } catch (err) {
      console.error('Failed to load messages:', err);
      setError(err instanceof Error ? err.message : 'Failed to load messages');
    } finally {
      setLoadingMessages(false);
    }
  };

  const handleSendMessage = async (message: string) => {
    if (!sessionId) return;

    try {
      setError(null);
      await onSendMessage(sessionId, message);
      // Reload messages after sending
      await loadMessages();
    } catch (err) {
      console.error('Failed to send message:', err);
      setError(err instanceof Error ? err.message : 'Failed to send message');
      throw err; // Re-throw to let MessageInput handle it
    }
  };

  const handleExecuteCommand = async (command: string, args: string) => {
    if (!sessionId || !onExecuteCommand) return;

    try {
      setError(null);
      await onExecuteCommand(sessionId, command, args);
      // Reload messages after command execution
      await loadMessages();
    } catch (err) {
      console.error('Failed to execute command:', err);
      setError(err instanceof Error ? err.message : 'Failed to execute command');
      throw err; // Re-throw to let MessageInput handle it
    }
  };

  const handleRefresh = () => {
    if (sessionId) {
      loadMessages();
    }
  };

  if (!sessionId) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <MessageSquare className="w-12 h-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No Session Selected
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Select a session from the sidebar to start chatting with OpenCode
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white dark:bg-gray-900">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-3">
          <MessageSquare className="w-5 h-5 text-gray-600 dark:text-gray-400" />
          <h2 className="font-medium text-gray-900 dark:text-white">
            Chat
          </h2>
          {messages.length > 0 && (
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {messages.length} message{messages.length !== 1 ? 's' : ''}
            </span>
          )}
        </div>

        {/* Chat Controls */}
        <ChatControls connectionStatus={connectionStatus} />
        
        <button
          onClick={handleRefresh}
          disabled={loadingMessages}
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md disabled:opacity-50"
          aria-label="Refresh messages"
        >
          <RefreshCw className={`w-4 h-4 ${loadingMessages ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {/* Messages */}
      <div 
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto"
      >
        {loadingMessages ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <RefreshCw className="w-6 h-6 mx-auto mb-2 animate-spin text-gray-400" />
              <p className="text-gray-600 dark:text-gray-400">Loading messages...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="text-red-600 dark:text-red-400 mb-2">
                Failed to load messages
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                {error}
              </p>
              <button
                onClick={handleRefresh}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Try Again
              </button>
            </div>
          </div>
        ) : messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <MessageSquare className="w-8 h-8 mx-auto mb-2 text-gray-400" />
              <p className="text-gray-600 dark:text-gray-400">
                No messages yet. Start a conversation!
              </p>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {messages.map((message, index) => (
              <Message
                key={message.info.id}
                message={message}
                isLast={index === messages.length - 1}
              />
            ))}
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Error banner */}
      {error && (
        <div className="p-3 bg-red-50 dark:bg-red-900/20 border-t border-red-200 dark:border-red-800">
          <div className="text-sm text-red-700 dark:text-red-400">
            {error}
          </div>
        </div>
      )}

      {/* Message input */}
      <MessageInput
        onSendMessage={handleSendMessage}
        onExecuteCommand={handleExecuteCommand}
        isLoading={isLoading}
        onStop={onStop}
        disabled={!sessionId || loadingMessages}
        placeholder={loadingMessages ? "Loading messages..." : "Type your message..."}
        sessionId={sessionId}
      />
    </div>
  );
}
