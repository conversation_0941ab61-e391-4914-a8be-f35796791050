'use client';

import { useState } from 'react';
import { Ch<PERSON>ronDown, ChevronRight, Wrench, Co<PERSON>, Check, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';

interface ToolInvocationPartProps {
  id: string;
  toolCallId: string;
  toolName: string;
  args: Record<string, any>;
  state: {
    status: "running" | "success" | "error";
    metadata?: Record<string, any>;
  };
  result?: {
    title: string;
    output: string;
    metadata: Record<string, any>;
  };
  messageID: string;
  sessionID: string;
}

export function ToolInvocationPart({ 
  id, 
  toolCallId, 
  toolName, 
  args, 
  state, 
  result, 
  messageID, 
  sessionID 
}: ToolInvocationPartProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [copiedArgs, setCopiedArgs] = useState(false);
  const [copiedResult, setCopiedResult] = useState(false);

  const copyArgs = async () => {
    try {
      await navigator.clipboard.writeText(JSON.stringify(args, null, 2));
      setCopiedArgs(true);
      setTimeout(() => setCopiedArgs(false), 2000);
    } catch (error) {
      console.error('Failed to copy args:', error);
    }
  };

  const copyResult = async () => {
    try {
      const content = result?.output || 'No output';
      await navigator.clipboard.writeText(content);
      setCopiedResult(true);
      setTimeout(() => setCopiedResult(false), 2000);
    } catch (error) {
      console.error('Failed to copy result:', error);
    }
  };

  const getStatusIcon = () => {
    switch (state.status) {
      case 'running':
        return <Loader2 className="w-4 h-4 text-blue-600 dark:text-blue-400 animate-spin" />;
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-600 dark:text-red-400" />;
      default:
        return <Wrench className="w-4 h-4 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getStatusColor = () => {
    switch (state.status) {
      case 'running':
        return 'border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-900/20';
      case 'success':
        return 'border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20';
      case 'error':
        return 'border-red-200 dark:border-red-800 bg-red-50 dark:bg-red-900/20';
      default:
        return 'border-gray-200 dark:border-gray-800 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const getHeaderHoverColor = () => {
    switch (state.status) {
      case 'running':
        return 'hover:bg-blue-100 dark:hover:bg-blue-900/30';
      case 'success':
        return 'hover:bg-green-100 dark:hover:bg-green-900/30';
      case 'error':
        return 'hover:bg-red-100 dark:hover:bg-red-900/30';
      default:
        return 'hover:bg-gray-100 dark:hover:bg-gray-900/30';
    }
  };

  return (
    <div className={`my-3 border rounded-lg ${getStatusColor()}`}>
      {/* Header */}
      <div 
        className={`flex items-center justify-between p-3 cursor-pointer ${getHeaderHoverColor()} transition-colors`}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-2">
          {getStatusIcon()}
          <span className="text-sm font-medium text-gray-800 dark:text-gray-200">
            {toolName}
          </span>
          <span className="text-xs text-gray-600 dark:text-gray-400">
            {state.status}
          </span>
          {result?.title && (
            <span className="text-xs text-gray-500 dark:text-gray-500">
              • {result.title}
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {isExpanded ? (
            <ChevronDown className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          ) : (
            <ChevronRight className="w-4 h-4 text-gray-600 dark:text-gray-400" />
          )}
        </div>
      </div>

      {/* Content */}
      {isExpanded && (
        <div className="px-3 pb-3 space-y-3">
          {/* Tool Arguments */}
          {Object.keys(args).length > 0 && (
            <div className="bg-white dark:bg-gray-900 rounded border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between px-3 py-2 bg-gray-100 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                  Arguments
                </span>
                <button
                  onClick={copyArgs}
                  className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 flex items-center gap-1"
                >
                  {copiedArgs ? (
                    <Check className="w-3 h-3 text-green-600" />
                  ) : (
                    <Copy className="w-3 h-3" />
                  )}
                  Copy
                </button>
              </div>
              <pre className="p-3 overflow-x-auto">
                <code className="text-sm font-mono text-gray-800 dark:text-gray-200">
                  {JSON.stringify(args, null, 2)}
                </code>
              </pre>
            </div>
          )}

          {/* Tool Result */}
          {result && (
            <div className="bg-white dark:bg-gray-900 rounded border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between px-3 py-2 bg-gray-100 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                  Result
                </span>
                <button
                  onClick={copyResult}
                  className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 flex items-center gap-1"
                >
                  {copiedResult ? (
                    <Check className="w-3 h-3 text-green-600" />
                  ) : (
                    <Copy className="w-3 h-3" />
                  )}
                  Copy
                </button>
              </div>
              <div className="p-3">
                <div className="prose prose-sm max-w-none dark:prose-invert">
                  <div className="whitespace-pre-wrap break-words text-gray-700 dark:text-gray-300">
                    {result.output}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Metadata */}
          {(state.metadata || result?.metadata) && (
            <div className="text-xs text-gray-500 dark:text-gray-400">
              <div className="font-medium mb-1">Metadata:</div>
              <pre className="text-xs">
                {JSON.stringify({ 
                  state: state.metadata, 
                  result: result?.metadata 
                }, null, 2)}
              </pre>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
