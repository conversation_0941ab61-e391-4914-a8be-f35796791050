'use client';

import { ExternalLink, Globe, Copy, Check } from 'lucide-react';
import { useState } from 'react';

interface SourceUrlPartProps {
  id: string;
  url: string;
  title?: string;
  messageID: string;
  sessionID: string;
}

export function SourceUrlPart({ id, url, title, messageID, sessionID }: SourceUrlPartProps) {
  const [copied, setCopied] = useState(false);

  const copyUrl = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy URL:', error);
    }
  };

  const getDomain = (url: string) => {
    try {
      return new URL(url).hostname;
    } catch {
      return url;
    }
  };

  const openUrl = () => {
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <div className="my-3 border border-indigo-200 dark:border-indigo-800 rounded-lg bg-indigo-50 dark:bg-indigo-900/20 p-3">
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 mt-0.5">
          <Globe className="w-4 h-4 text-indigo-600 dark:text-indigo-400" />
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1 min-w-0">
              {title && (
                <div className="text-sm font-medium text-indigo-800 dark:text-indigo-200 mb-1">
                  {title}
                </div>
              )}
              
              <div className="text-xs text-indigo-600 dark:text-indigo-400 font-mono break-all">
                {url}
              </div>
              
              <div className="text-xs text-indigo-500 dark:text-indigo-500 mt-1">
                {getDomain(url)}
              </div>
            </div>
            
            <div className="flex items-center gap-1 flex-shrink-0">
              <button
                onClick={copyUrl}
                className="p-1 hover:bg-indigo-200 dark:hover:bg-indigo-800 rounded transition-colors"
                aria-label="Copy URL"
              >
                {copied ? (
                  <Check className="w-3 h-3 text-green-600" />
                ) : (
                  <Copy className="w-3 h-3 text-indigo-600 dark:text-indigo-400" />
                )}
              </button>
              
              <button
                onClick={openUrl}
                className="p-1 hover:bg-indigo-200 dark:hover:bg-indigo-800 rounded transition-colors"
                aria-label="Open URL"
              >
                <ExternalLink className="w-3 h-3 text-indigo-600 dark:text-indigo-400" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
