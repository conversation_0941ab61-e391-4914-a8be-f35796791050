'use client';

import { Zap, Check, Clock, DollarSign } from 'lucide-react';

interface StepStartPartProps {
  id: string;
  step?: number;
  messageID: string;
  sessionID: string;
}

interface StepFinishPartProps {
  id: string;
  cost: number;
  tokens: {
    input: number;
    output: number;
    reasoning: number;
    cache: {
      read: number;
      write: number;
    };
  };
  messageID: string;
  sessionID: string;
}

export function StepStartPart({ id, step, messageID, sessionID }: StepStartPartProps) {
  return (
    <div className="flex items-center gap-2 text-xs text-blue-600 dark:text-blue-400 mb-2 py-1">
      <Zap className="w-3 h-3 animate-pulse" />
      <span className="font-medium">
        {step ? `Step ${step} - ` : ''}Processing...
      </span>
    </div>
  );
}

export function StepFinishPart({ id, cost, tokens, messageID, sessionID }: StepFinishPartProps) {
  const totalTokens = tokens.input + tokens.output + tokens.reasoning;
  const cacheTokens = tokens.cache.read + tokens.cache.write;

  const formatCost = (cost: number) => {
    if (cost === 0) return 'Free';
    if (cost < 0.001) return '<$0.001';
    return `$${cost.toFixed(4)}`;
  };

  const formatTokens = (count: number) => {
    if (count < 1000) return count.toString();
    if (count < 1000000) return `${(count / 1000).toFixed(1)}K`;
    return `${(count / 1000000).toFixed(1)}M`;
  };

  return (
    <div className="flex items-center gap-4 text-xs text-green-600 dark:text-green-400 mt-2 py-2 border-t border-gray-200 dark:border-gray-700">
      <div className="flex items-center gap-1">
        <Check className="w-3 h-3" />
        <span className="font-medium">Completed</span>
      </div>
      
      {totalTokens > 0 && (
        <div className="flex items-center gap-1">
          <Clock className="w-3 h-3" />
          <span>{formatTokens(totalTokens)} tokens</span>
          {tokens.reasoning > 0 && (
            <span className="text-purple-600 dark:text-purple-400">
              ({formatTokens(tokens.reasoning)} reasoning)
            </span>
          )}
        </div>
      )}
      
      {cacheTokens > 0 && (
        <div className="flex items-center gap-1 text-orange-600 dark:text-orange-400">
          <span>📦 {formatTokens(cacheTokens)} cached</span>
        </div>
      )}
      
      {cost > 0 && (
        <div className="flex items-center gap-1">
          <DollarSign className="w-3 h-3" />
          <span>{formatCost(cost)}</span>
        </div>
      )}
    </div>
  );
}
