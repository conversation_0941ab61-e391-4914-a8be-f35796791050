'use client';

import { useState } from 'react';
import { Copy, Check } from 'lucide-react';

interface TextPartProps {
  id: string;
  text: string;
  messageID: string;
  sessionID: string;
  time?: {
    start?: number;
    end?: number;
  };
}

export function TextPart({ id, text, messageID, sessionID, time }: TextPartProps) {
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  const renderMarkdown = (content: string) => {
    // Simple markdown-like rendering for code blocks
    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
    const inlineCodeRegex = /`([^`]+)`/g;
    
    let parts: React.ReactNode[] = [];
    let lastIndex = 0;
    let match;

    // Handle code blocks
    while ((match = codeBlockRegex.exec(content)) !== null) {
      // Add text before code block
      if (match.index > lastIndex) {
        const beforeText = content.slice(lastIndex, match.index);
        parts.push(renderInlineCode(beforeText, parts.length));
      }

      // Add code block
      const language = match[1] || 'text';
      const code = match[2];
      parts.push(
        <div key={parts.length} className="my-3">
          <div className="bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden">
            <div className="flex items-center justify-between px-3 py-2 bg-gray-200 dark:bg-gray-700 border-b border-gray-300 dark:border-gray-600">
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                {language}
              </span>
              <button
                onClick={() => navigator.clipboard.writeText(code)}
                className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 flex items-center gap-1"
              >
                <Copy className="w-3 h-3" />
                Copy
              </button>
            </div>
            <pre className="p-3 overflow-x-auto">
              <code className="text-sm font-mono text-gray-800 dark:text-gray-200">
                {code}
              </code>
            </pre>
          </div>
        </div>
      );

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < content.length) {
      const remainingText = content.slice(lastIndex);
      parts.push(renderInlineCode(remainingText, parts.length));
    }

    return parts.length > 0 ? parts : renderInlineCode(content, 0);
  };

  const renderInlineCode = (content: string, key: number) => {
    const inlineCodeRegex = /`([^`]+)`/g;
    const parts: React.ReactNode[] = [];
    let lastIndex = 0;
    let match;

    while ((match = inlineCodeRegex.exec(content)) !== null) {
      // Add text before inline code
      if (match.index > lastIndex) {
        const beforeText = content.slice(lastIndex, match.index);
        parts.push(beforeText);
      }

      // Add inline code
      parts.push(
        <code 
          key={`${key}-${parts.length}`}
          className="px-1.5 py-0.5 bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 rounded text-sm font-mono"
        >
          {match[1]}
        </code>
      );

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < content.length) {
      parts.push(content.slice(lastIndex));
    }

    return parts.length > 0 ? (
      <span key={key}>{parts}</span>
    ) : (
      <span key={key}>{content}</span>
    );
  };

  return (
    <div className="group relative">
      <div className="prose prose-sm max-w-none dark:prose-invert">
        <div className="whitespace-pre-wrap break-words">
          {renderMarkdown(text)}
        </div>
      </div>
      
      {/* Copy button */}
      <button
        onClick={copyToClipboard}
        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
        aria-label="Copy text"
      >
        {copied ? (
          <Check className="w-3 h-3 text-green-600" />
        ) : (
          <Copy className="w-3 h-3 text-gray-500" />
        )}
      </button>
    </div>
  );
}
