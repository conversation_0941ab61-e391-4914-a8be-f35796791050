'use client';

import { useState } from 'react';
import { ChevronDown, ChevronRight, File, Copy, Check, ExternalLink } from 'lucide-react';

interface FilePartProps {
  id: string;
  path: string;
  content: string;
  language?: string;
  messageID: string;
  sessionID: string;
}

export function FilePart({ id, path, content, language, messageID, sessionID }: FilePartProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [copied, setCopied] = useState(false);

  const copyContent = async () => {
    try {
      await navigator.clipboard.writeText(content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy file content:', error);
    }
  };

  const getFileExtension = (filePath: string) => {
    const parts = filePath.split('.');
    return parts.length > 1 ? parts[parts.length - 1] : '';
  };

  const getLanguageFromPath = (filePath: string) => {
    if (language) return language;
    
    const ext = getFileExtension(filePath).toLowerCase();
    const languageMap: Record<string, string> = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'py': 'python',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'java': 'java',
      'c': 'c',
      'cpp': 'cpp',
      'h': 'c',
      'hpp': 'cpp',
      'css': 'css',
      'scss': 'scss',
      'html': 'html',
      'xml': 'xml',
      'json': 'json',
      'yaml': 'yaml',
      'yml': 'yaml',
      'md': 'markdown',
      'sh': 'bash',
      'bash': 'bash',
      'zsh': 'bash',
      'fish': 'bash',
      'sql': 'sql',
      'php': 'php',
      'swift': 'swift',
      'kt': 'kotlin',
      'scala': 'scala',
      'clj': 'clojure',
      'hs': 'haskell',
      'elm': 'elm',
      'dart': 'dart',
      'r': 'r',
      'matlab': 'matlab',
      'tex': 'latex',
      'dockerfile': 'dockerfile',
      'makefile': 'makefile',
    };
    
    return languageMap[ext] || 'text';
  };

  const getFileName = (filePath: string) => {
    const parts = filePath.split('/');
    return parts[parts.length - 1];
  };

  const getFileIcon = () => {
    const ext = getFileExtension(path).toLowerCase();
    const iconMap: Record<string, string> = {
      'js': '🟨',
      'jsx': '🟨',
      'ts': '🔷',
      'tsx': '🔷',
      'py': '🐍',
      'rb': '💎',
      'go': '🐹',
      'rs': '🦀',
      'java': '☕',
      'c': '⚙️',
      'cpp': '⚙️',
      'css': '🎨',
      'html': '🌐',
      'json': '📋',
      'md': '📝',
      'sh': '🐚',
      'sql': '🗃️',
      'php': '🐘',
    };
    
    return iconMap[ext] || '📄';
  };

  const formatFileSize = (content: string) => {
    const bytes = new Blob([content]).size;
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  const getLineCount = (content: string) => {
    return content.split('\n').length;
  };

  const detectedLanguage = getLanguageFromPath(path);

  return (
    <div className="my-3 border border-blue-200 dark:border-blue-800 rounded-lg bg-blue-50 dark:bg-blue-900/20">
      {/* Header */}
      <div 
        className="flex items-center justify-between p-3 cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-2">
          <span className="text-lg">{getFileIcon()}</span>
          <File className="w-4 h-4 text-blue-600 dark:text-blue-400" />
          <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
            {getFileName(path)}
          </span>
          <span className="text-xs text-blue-600 dark:text-blue-400">
            {detectedLanguage}
          </span>
          <span className="text-xs text-blue-600 dark:text-blue-400">
            • {getLineCount(content)} lines • {formatFileSize(content)}
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              copyContent();
            }}
            className="p-1 hover:bg-blue-200 dark:hover:bg-blue-800 rounded transition-colors"
            aria-label="Copy file content"
          >
            {copied ? (
              <Check className="w-3 h-3 text-green-600" />
            ) : (
              <Copy className="w-3 h-3 text-blue-600 dark:text-blue-400" />
            )}
          </button>
          
          <button
            onClick={(e) => {
              e.stopPropagation();
              // TODO: Implement file opening in editor
            }}
            className="p-1 hover:bg-blue-200 dark:hover:bg-blue-800 rounded transition-colors"
            aria-label="Open file"
          >
            <ExternalLink className="w-3 h-3 text-blue-600 dark:text-blue-400" />
          </button>
          
          {isExpanded ? (
            <ChevronDown className="w-4 h-4 text-blue-600 dark:text-blue-400" />
          ) : (
            <ChevronRight className="w-4 h-4 text-blue-600 dark:text-blue-400" />
          )}
        </div>
      </div>

      {/* File path */}
      <div className="px-3 pb-2">
        <div className="text-xs text-blue-700 dark:text-blue-300 font-mono bg-blue-100 dark:bg-blue-900/40 px-2 py-1 rounded">
          {path}
        </div>
      </div>

      {/* Content */}
      {isExpanded && (
        <div className="px-3 pb-3">
          <div className="bg-white dark:bg-gray-900 rounded border border-blue-200 dark:border-blue-700 overflow-hidden">
            <div className="flex items-center justify-between px-3 py-2 bg-gray-100 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                {detectedLanguage}
              </span>
              <button
                onClick={copyContent}
                className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 flex items-center gap-1"
              >
                {copied ? (
                  <Check className="w-3 h-3 text-green-600" />
                ) : (
                  <Copy className="w-3 h-3" />
                )}
                Copy
              </button>
            </div>
            <div className="max-h-96 overflow-y-auto">
              <pre className="p-3 overflow-x-auto">
                <code className="text-sm font-mono text-gray-800 dark:text-gray-200">
                  {content}
                </code>
              </pre>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
