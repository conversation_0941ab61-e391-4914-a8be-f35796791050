'use client';

import { useState } from 'react';
import { ChevronDown, ChevronR<PERSON>, <PERSON>, <PERSON><PERSON>, Check } from 'lucide-react';

interface ReasoningPartProps {
  id: string;
  text: string;
  messageID: string;
  sessionID: string;
}

export function ReasoningPart({ id, text, messageID, sessionID }: ReasoningPartProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [copied, setCopied] = useState(false);

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy reasoning:', error);
    }
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="my-3 border border-purple-200 dark:border-purple-800 rounded-lg bg-purple-50 dark:bg-purple-900/20">
      {/* Header */}
      <div 
        className="flex items-center justify-between p-3 cursor-pointer hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors"
        onClick={toggleExpanded}
      >
        <div className="flex items-center gap-2">
          <Brain className="w-4 h-4 text-purple-600 dark:text-purple-400" />
          <span className="text-sm font-medium text-purple-800 dark:text-purple-200">
            AI Reasoning
          </span>
          <span className="text-xs text-purple-600 dark:text-purple-400">
            ({text.length} characters)
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              copyToClipboard();
            }}
            className="p-1 hover:bg-purple-200 dark:hover:bg-purple-800 rounded transition-colors"
            aria-label="Copy reasoning"
          >
            {copied ? (
              <Check className="w-3 h-3 text-green-600" />
            ) : (
              <Copy className="w-3 h-3 text-purple-600 dark:text-purple-400" />
            )}
          </button>
          
          {isExpanded ? (
            <ChevronDown className="w-4 h-4 text-purple-600 dark:text-purple-400" />
          ) : (
            <ChevronRight className="w-4 h-4 text-purple-600 dark:text-purple-400" />
          )}
        </div>
      </div>

      {/* Content */}
      {isExpanded && (
        <div className="px-3 pb-3">
          <div className="bg-white dark:bg-gray-900 rounded border border-purple-200 dark:border-purple-700 p-3">
            <div className="prose prose-sm max-w-none dark:prose-invert">
              <div className="whitespace-pre-wrap break-words text-gray-700 dark:text-gray-300">
                {text}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
