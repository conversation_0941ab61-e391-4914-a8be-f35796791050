'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { AgentSelector, ModelSelector, AgentConfigDialog } from '@/components/agents';

interface ChatControlsProps {
  connectionStatus?: 'disconnected' | 'connected' | 'testing';
  className?: string;
}

export function ChatControls({ 
  connectionStatus = 'disconnected',
  className = '' 
}: ChatControlsProps) {
  const [selectedAgent, setSelectedAgent] = useState<string>('general');
  const [selectedModel, setSelectedModel] = useState<{ providerID: string; modelID: string } | undefined>();
  const [configAgent, setConfigAgent] = useState<any>(null);
  const [showAgentConfig, setShowAgentConfig] = useState(false);

  const handleAgentConfigure = (agent: any) => {
    setConfigAgent(agent);
    setShowAgentConfig(true);
  };

  const handleAgentConfigSave = (agent: any) => {
    // TODO: Implement agent configuration save
    console.log('Save agent config:', agent);
    setShowAgentConfig(false);
  };

  if (connectionStatus !== 'connected') {
    return null;
  }

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      {/* Agent Selection */}
      <div className="flex items-center gap-2">
        <Bot className="w-4 h-4 text-gray-500 dark:text-gray-400" />
        <div className="min-w-0">
          <AgentSelector
            selectedAgent={selectedAgent}
            onAgentSelect={setSelectedAgent}
            onAgentConfigure={handleAgentConfigure}
            className="min-w-[200px]"
          />
        </div>
      </div>

      {/* Model Selection */}
      <div className="flex items-center gap-2">
        <Brain className="w-4 h-4 text-gray-500 dark:text-gray-400" />
        <div className="min-w-0">
          <ModelSelector
            selectedModel={selectedModel}
            onModelSelect={(providerID, modelID) => setSelectedModel({ providerID, modelID })}
            className="min-w-[250px]"
          />
        </div>
      </div>

      {/* Agent Configuration Dialog */}
      <AgentConfigDialog
        agent={configAgent}
        isOpen={showAgentConfig}
        onClose={() => setShowAgentConfig(false)}
        onSave={handleAgentConfigSave}
      />
    </div>
  );
}
