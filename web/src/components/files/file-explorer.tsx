'use client';

import React, { useState, useEffect } from 'react';
import { 
  Folder, 
  File, 
  ChevronRight, 
  ChevronDown, 
  Search,
  RefreshCw,
  GitBranch,
  Plus,
  Minus,
  Edit3,
  Eye,
  Download,
  Upload,
  MoreHorizontal
} from 'lucide-react';
import { apiService } from '@/lib/api-service';

interface FileNode {
  name: string;
  path: string;
  absolute: string;
  type: 'file' | 'directory';
  ignored: boolean;
}

interface FileExplorerProps {
  onFileSelect?: (file: FileNode) => void;
  onFileOpen?: (file: FileNode) => void;
  selectedPath?: string;
}

export function FileExplorer({ onFileSelect, onFileOpen, selectedPath }: FileExplorerProps) {
  const [files, setFiles] = useState<FileNode[]>([]);
  const [expandedDirs, setExpandedDirs] = useState<Set<string>>(new Set(['.']));
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPath, setCurrentPath] = useState('.');
  const [error, setError] = useState<string | null>(null);

  const loadFiles = async (path: string = '.') => {
    try {
      setLoading(true);
      setError(null);
      const fileList = await apiService.listFiles(path);
      setFiles(fileList);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load files');
      console.error('Failed to load files:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadFiles(currentPath);
  }, [currentPath]);

  const toggleDirectory = async (dirPath: string) => {
    const newExpanded = new Set(expandedDirs);
    if (expandedDirs.has(dirPath)) {
      newExpanded.delete(dirPath);
    } else {
      newExpanded.add(dirPath);
      // Load directory contents if not already loaded
      try {
        await loadFiles(dirPath);
      } catch (err) {
        console.error('Failed to load directory:', err);
      }
    }
    setExpandedDirs(newExpanded);
  };

  const handleFileClick = (file: FileNode) => {
    if (file.type === 'directory') {
      toggleDirectory(file.path);
    } else {
      onFileSelect?.(file);
    }
  };

  const handleFileDoubleClick = (file: FileNode) => {
    if (file.type === 'file') {
      onFileOpen?.(file);
    }
  };

  const getFileIcon = (file: FileNode) => {
    if (file.type === 'directory') {
      return expandedDirs.has(file.path) ? (
        <ChevronDown className="w-4 h-4 text-blue-600" />
      ) : (
        <ChevronRight className="w-4 h-4 text-blue-600" />
      );
    }

    // File type icons based on extension
    const ext = file.name.split('.').pop()?.toLowerCase();
    const iconClass = "w-4 h-4";
    
    switch (ext) {
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx':
        return <File className={`${iconClass} text-yellow-600`} />;
      case 'py':
        return <File className={`${iconClass} text-blue-600`} />;
      case 'json':
        return <File className={`${iconClass} text-green-600`} />;
      case 'md':
        return <File className={`${iconClass} text-purple-600`} />;
      case 'css':
      case 'scss':
        return <File className={`${iconClass} text-pink-600`} />;
      case 'html':
        return <File className={`${iconClass} text-orange-600`} />;
      default:
        return <File className={`${iconClass} text-gray-600`} />;
    }
  };

  const filteredFiles = files.filter(file => 
    file.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className="flex flex-col h-full bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700">
      {/* Header */}
      <div className="p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-sm font-semibold text-gray-900 dark:text-white">Files</h2>
          <div className="flex items-center space-x-1">
            <button
              onClick={() => loadFiles(currentPath)}
              disabled={loading}
              className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 disabled:opacity-50 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title="Refresh"
            >
              <RefreshCw className={`w-3.5 h-3.5 ${loading ? 'animate-spin' : ''}`} />
            </button>
            <button
              className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title="More options"
            >
              <MoreHorizontal className="w-3.5 h-3.5" />
            </button>
          </div>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search files..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* File Tree */}
      <div className="flex-1 overflow-y-auto">
        {error && (
          <div className="p-3 text-sm text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800">
            {error}
          </div>
        )}

        {loading && files.length === 0 ? (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            <RefreshCw className="w-5 h-5 animate-spin mx-auto mb-2" />
            <div className="text-xs">Loading files...</div>
          </div>
        ) : (
          <div className="py-1">
            {filteredFiles.map((file) => (
              <div
                key={file.path}
                className={`flex items-center space-x-2 px-3 py-1.5 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 ${
                  selectedPath === file.path ? 'bg-blue-100 dark:bg-blue-900/30 border-r-2 border-blue-500' : ''
                } ${file.ignored ? 'opacity-50' : ''}`}
                onClick={() => handleFileClick(file)}
                onDoubleClick={() => handleFileDoubleClick(file)}
                title={file.absolute}
              >
                <div className="flex-shrink-0">
                  {getFileIcon(file)}
                </div>
                <span className="text-sm text-gray-900 dark:text-white truncate flex-1 min-w-0">
                  {file.name}
                </span>
                {file.type === 'directory' && (
                  <ChevronRight className="w-3.5 h-3.5 text-gray-400 flex-shrink-0" />
                )}
              </div>
            ))}

            {filteredFiles.length === 0 && !loading && (
              <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                <div className="text-sm">{searchQuery ? 'No files match your search' : 'No files found'}</div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-3 py-2 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800">
        {files.length} {files.length === 1 ? 'item' : 'items'}
      </div>
    </div>
  );
}
