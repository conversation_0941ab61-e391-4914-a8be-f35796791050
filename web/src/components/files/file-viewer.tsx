'use client';

import React, { useState, useEffect } from 'react';
import { 
  File, 
  Download, 
  Copy, 
  Edit3, 
  Eye, 
  EyeOff,
  Search,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  X,
  Check,
  AlertCircle
} from 'lucide-react';
import { apiService } from '@/lib/api-service';

interface FileNode {
  name: string;
  path: string;
  absolute: string;
  type: 'file' | 'directory';
  ignored: boolean;
}

interface FileContent {
  content: string;
  diff?: string;
  patch?: any;
}

interface FileViewerProps {
  file: FileNode | null;
  onClose?: () => void;
  onEdit?: (file: FileNode) => void;
}

export function FileViewer({ file, onClose, onEdit }: FileViewerProps) {
  const [content, setContent] = useState<FileContent | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showLineNumbers, setShowLineNumbers] = useState(true);
  const [fontSize, setFontSize] = useState(14);
  const [searchQuery, setSearchQuery] = useState('');
  const [copySuccess, setCopySuccess] = useState(false);

  useEffect(() => {
    if (file && file.type === 'file') {
      loadFileContent();
    } else {
      setContent(null);
      setError(null);
    }
  }, [file]);

  const loadFileContent = async () => {
    if (!file) return;

    try {
      setLoading(true);
      setError(null);
      const fileContent = await apiService.readFile(file.path);
      setContent(fileContent);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load file content');
      console.error('Failed to load file content:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCopy = async () => {
    if (!content?.content) return;

    try {
      await navigator.clipboard.writeText(content.content);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } catch (err) {
      console.error('Failed to copy content:', err);
    }
  };

  const handleDownload = () => {
    if (!content?.content || !file) return;

    const blob = new Blob([content.content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getLanguageFromExtension = (filename: string): string => {
    const ext = filename.split('.').pop()?.toLowerCase();
    const languageMap: Record<string, string> = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'py': 'python',
      'json': 'json',
      'md': 'markdown',
      'css': 'css',
      'scss': 'scss',
      'html': 'html',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      'sh': 'bash',
      'sql': 'sql',
      'go': 'go',
      'rs': 'rust',
      'java': 'java',
      'c': 'c',
      'cpp': 'cpp',
      'h': 'c',
      'hpp': 'cpp',
    };
    return languageMap[ext || ''] || 'text';
  };

  const highlightSearchMatches = (text: string, query: string): string => {
    if (!query.trim()) return text;
    
    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800">$1</mark>');
  };

  const renderContent = () => {
    if (!content) return null;

    const lines = content.content.split('\n');
    const language = getLanguageFromExtension(file?.name || '');

    return (
      <div className="h-full">
        <pre
          className="h-full text-sm font-mono p-4 bg-gray-50 dark:bg-gray-800"
          style={{ fontSize: `${fontSize}px` }}
        >
          {lines.map((line, index) => (
            <div key={index} className="flex min-h-[1.5em]">
              {showLineNumbers && (
                <span className="text-gray-400 dark:text-gray-500 select-none w-12 text-right pr-4 flex-shrink-0 leading-relaxed">
                  {index + 1}
                </span>
              )}
              <code
                className="flex-1 text-gray-900 dark:text-gray-100 leading-relaxed"
                dangerouslySetInnerHTML={{
                  __html: highlightSearchMatches(line || ' ', searchQuery)
                }}
              />
            </div>
          ))}
        </pre>
      </div>
    );
  };

  if (!file) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center text-gray-500 dark:text-gray-400">
          <File className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <p className="text-lg font-medium">No file selected</p>
          <p className="text-sm">Select a file from the explorer to view its content</p>
        </div>
      </div>
    );
  }

  if (file.type === 'directory') {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center text-gray-500 dark:text-gray-400">
          <File className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <p className="text-lg font-medium">Directory selected</p>
          <p className="text-sm">Select a file to view its content</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col bg-white dark:bg-gray-900">
      {/* Header */}
      <div className="p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3 min-w-0 flex-1">
            <File className="w-4 h-4 text-gray-600 dark:text-gray-400 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <h2 className="text-sm font-semibold text-gray-900 dark:text-white truncate">
                {file.name}
              </h2>
              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                {file.path}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-1 flex-shrink-0">
            {/* Font size controls */}
            <div className="flex items-center space-x-1">
              <button
                onClick={() => setFontSize(Math.max(10, fontSize - 2))}
                className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                title="Decrease font size"
              >
                <ZoomOut className="w-3.5 h-3.5" />
              </button>
              <span className="text-xs text-gray-500 dark:text-gray-400 min-w-[2.5rem] text-center">
                {fontSize}px
              </span>
              <button
                onClick={() => setFontSize(Math.min(24, fontSize + 2))}
                className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                title="Increase font size"
              >
                <ZoomIn className="w-3.5 h-3.5" />
              </button>
            </div>

            {/* Line numbers toggle */}
            <button
              onClick={() => setShowLineNumbers(!showLineNumbers)}
              className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title={showLineNumbers ? "Hide line numbers" : "Show line numbers"}
            >
              {showLineNumbers ? <EyeOff className="w-3.5 h-3.5" /> : <Eye className="w-3.5 h-3.5" />}
            </button>

            {/* Copy button */}
            <button
              onClick={handleCopy}
              disabled={!content?.content}
              className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 disabled:opacity-50 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title="Copy content"
            >
              {copySuccess ? <Check className="w-3.5 h-3.5 text-green-600" /> : <Copy className="w-3.5 h-3.5" />}
            </button>

            {/* Download button */}
            <button
              onClick={handleDownload}
              disabled={!content?.content}
              className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 disabled:opacity-50 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title="Download file"
            >
              <Download className="w-3.5 h-3.5" />
            </button>

            {/* Edit button */}
            <button
              onClick={() => onEdit?.(file)}
              className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
              title="Edit file"
            >
              <Edit3 className="w-3.5 h-3.5" />
            </button>

            {/* Close button */}
            {onClose && (
              <button
                onClick={onClose}
                className="p-1.5 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                title="Close"
              >
                <X className="w-3.5 h-3.5" />
              </button>
            )}
          </div>
        </div>

        {/* Search bar */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search in file..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500 dark:text-gray-400">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p>Loading file content...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-red-600 dark:text-red-400">
              <AlertCircle className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">Failed to load file</p>
              <p className="text-sm">{error}</p>
              <button
                onClick={loadFileContent}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <RotateCcw className="w-4 h-4 inline mr-2" />
                Retry
              </button>
            </div>
          </div>
        ) : (
          <div className="h-full overflow-y-auto">
            {renderContent()}
          </div>
        )}
      </div>

      {/* Footer */}
      {content && (
        <div className="p-2 border-t border-gray-200 dark:border-gray-700 text-xs text-gray-500 dark:text-gray-400 flex justify-between">
          <span>
            {content.content.split('\n').length} lines, {content.content.length} characters
          </span>
          <span>
            {getLanguageFromExtension(file.name)}
          </span>
        </div>
      )}
    </div>
  );
}
