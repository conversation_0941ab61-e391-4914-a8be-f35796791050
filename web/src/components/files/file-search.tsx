'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { 
  Search, 
  File, 
  Folder,
  X,
  Filter,
  ChevronDown,
  ChevronRight,
  Copy,
  ExternalLink,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import { apiService } from '@/lib/api-service';

interface SearchMatch {
  path: { text: string };
  lines: { text: string };
  line_number: number;
  absolute_offset: number;
  submatches: Array<{
    match: { text: string };
    start: number;
    end: number;
  }>;
}

interface Symbol {
  name: string;
  kind: number;
  location: {
    uri: string;
    range: {
      start: { line: number; character: number };
      end: { line: number; character: number };
    };
  };
}

interface FileSearchProps {
  onFileSelect?: (path: string, lineNumber?: number) => void;
  onClose?: () => void;
}

type SearchType = 'text' | 'files' | 'symbols';

export function FileSearch({ onFileSelect, onClose }: FileSearchProps) {
  const [searchType, setSearchType] = useState<SearchType>('text');
  const [query, setQuery] = useState('');
  const [textResults, setTextResults] = useState<SearchMatch[]>([]);
  const [fileResults, setFileResults] = useState<string[]>([]);
  const [symbolResults, setSymbolResults] = useState<Symbol[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expandedFiles, setExpandedFiles] = useState<Set<string>>(new Set());

  const debounce = useCallback((func: Function, delay: number) => {
    let timeoutId: NodeJS.Timeout;
    return (...args: any[]) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), delay);
    };
  }, []);

  const performSearch = async (searchQuery: string, type: SearchType) => {
    if (!searchQuery.trim()) {
      setTextResults([]);
      setFileResults([]);
      setSymbolResults([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      switch (type) {
        case 'text':
          const textMatches = await apiService.searchText(searchQuery);
          setTextResults(textMatches);
          break;
        case 'files':
          const files = await apiService.searchFiles(searchQuery);
          setFileResults(files);
          break;
        case 'symbols':
          const symbols = await apiService.searchSymbols(searchQuery);
          setSymbolResults(symbols);
          break;
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search failed');
      console.error('Search failed:', err);
    } finally {
      setLoading(false);
    }
  };

  const debouncedSearch = useCallback(
    debounce((searchQuery: string, type: SearchType) => {
      performSearch(searchQuery, type);
    }, 300),
    []
  );

  useEffect(() => {
    debouncedSearch(query, searchType);
  }, [query, searchType, debouncedSearch]);

  const toggleFileExpansion = (filePath: string) => {
    const newExpanded = new Set(expandedFiles);
    if (expandedFiles.has(filePath)) {
      newExpanded.delete(filePath);
    } else {
      newExpanded.add(filePath);
    }
    setExpandedFiles(newExpanded);
  };

  const handleResultClick = (path: string, lineNumber?: number) => {
    onFileSelect?.(path, lineNumber);
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  const getSymbolKindName = (kind: number): string => {
    const symbolKinds: Record<number, string> = {
      1: 'File',
      2: 'Module',
      3: 'Namespace',
      4: 'Package',
      5: 'Class',
      6: 'Method',
      7: 'Property',
      8: 'Field',
      9: 'Constructor',
      10: 'Enum',
      11: 'Interface',
      12: 'Function',
      13: 'Variable',
      14: 'Constant',
      15: 'String',
      16: 'Number',
      17: 'Boolean',
      18: 'Array',
      19: 'Object',
      20: 'Key',
      21: 'Null',
      22: 'EnumMember',
      23: 'Struct',
      24: 'Event',
      25: 'Operator',
      26: 'TypeParameter'
    };
    return symbolKinds[kind] || 'Unknown';
  };

  const highlightMatches = (text: string, matches: Array<{ start: number; end: number }>) => {
    if (!matches.length) return text;

    let result = '';
    let lastIndex = 0;

    matches.forEach(match => {
      result += text.slice(lastIndex, match.start);
      result += `<mark class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">${text.slice(match.start, match.end)}</mark>`;
      lastIndex = match.end;
    });

    result += text.slice(lastIndex);
    return result;
  };

  const groupTextResultsByFile = (results: SearchMatch[]) => {
    const grouped: Record<string, SearchMatch[]> = {};
    results.forEach(result => {
      const filePath = result.path.text;
      if (!grouped[filePath]) {
        grouped[filePath] = [];
      }
      grouped[filePath].push(result);
    });
    return grouped;
  };

  const renderTextResults = () => {
    const groupedResults = groupTextResultsByFile(textResults);
    
    return (
      <div className="space-y-2">
        {Object.entries(groupedResults).map(([filePath, matches]) => (
          <div key={filePath} className="border border-gray-200 dark:border-gray-700 rounded-lg">
            <div
              className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700"
              onClick={() => toggleFileExpansion(filePath)}
            >
              <div className="flex items-center space-x-2">
                {expandedFiles.has(filePath) ? (
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                ) : (
                  <ChevronRight className="w-4 h-4 text-gray-500" />
                )}
                <File className="w-4 h-4 text-blue-600" />
                <span className="font-medium text-gray-900 dark:text-white">{filePath}</span>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  ({matches.length} match{matches.length !== 1 ? 'es' : ''})
                </span>
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  copyToClipboard(filePath);
                }}
                className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                title="Copy file path"
              >
                <Copy className="w-4 h-4" />
              </button>
            </div>

            {expandedFiles.has(filePath) && (
              <div className="border-t border-gray-200 dark:border-gray-700">
                {matches.map((match, index) => (
                  <div
                    key={index}
                    className="p-3 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer border-b border-gray-100 dark:border-gray-700 last:border-b-0"
                    onClick={() => handleResultClick(filePath, match.line_number)}
                  >
                    <div className="flex items-start space-x-3">
                      <span className="text-sm text-gray-500 dark:text-gray-400 font-mono min-w-[3rem]">
                        {match.line_number}
                      </span>
                      <code
                        className="text-sm text-gray-900 dark:text-gray-100 font-mono flex-1"
                        dangerouslySetInnerHTML={{
                          __html: highlightMatches(match.lines.text.trim(), match.submatches.map(sm => ({
                            start: sm.start,
                            end: sm.end
                          })))
                        }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    );
  };

  const renderFileResults = () => (
    <div className="space-y-1">
      {fileResults.map((filePath, index) => (
        <div
          key={index}
          className="flex items-center justify-between p-3 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer rounded-lg"
          onClick={() => handleResultClick(filePath)}
        >
          <div className="flex items-center space-x-2">
            <File className="w-4 h-4 text-blue-600" />
            <span className="text-gray-900 dark:text-white">{filePath}</span>
          </div>
          <button
            onClick={(e) => {
              e.stopPropagation();
              copyToClipboard(filePath);
            }}
            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            title="Copy file path"
          >
            <Copy className="w-4 h-4" />
          </button>
        </div>
      ))}
    </div>
  );

  const renderSymbolResults = () => (
    <div className="space-y-1">
      {symbolResults.map((symbol, index) => (
        <div
          key={index}
          className="flex items-center justify-between p-3 hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer rounded-lg"
          onClick={() => handleResultClick(
            symbol.location.uri.replace('file://', ''),
            symbol.location.range.start.line + 1
          )}
        >
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 bg-purple-100 dark:bg-purple-900 rounded flex items-center justify-center">
              <span className="text-xs font-bold text-purple-600 dark:text-purple-400">
                {getSymbolKindName(symbol.kind).charAt(0)}
              </span>
            </div>
            <div>
              <div className="font-medium text-gray-900 dark:text-white">{symbol.name}</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {getSymbolKindName(symbol.kind)} in {symbol.location.uri.replace('file://', '')}
              </div>
            </div>
          </div>
          <button
            onClick={(e) => {
              e.stopPropagation();
              copyToClipboard(`${symbol.name} (${symbol.location.uri.replace('file://', '')}:${symbol.location.range.start.line + 1})`);
            }}
            className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            title="Copy symbol info"
          >
            <Copy className="w-4 h-4" />
          </button>
        </div>
      ))}
    </div>
  );

  const getResultCount = () => {
    switch (searchType) {
      case 'text':
        return textResults.length;
      case 'files':
        return fileResults.length;
      case 'symbols':
        return symbolResults.length;
      default:
        return 0;
    }
  };

  return (
    <div className="flex flex-col h-full bg-white dark:bg-gray-900 border-l border-gray-200 dark:border-gray-700">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Search</h2>
          <button
            onClick={onClose}
            className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <X className="w-4 h-4" />
          </button>
        </div>

        {/* Search Type Tabs */}
        <div className="flex space-x-1 mb-4 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
          {[
            { key: 'text', label: 'Text', icon: Search },
            { key: 'files', label: 'Files', icon: File },
            { key: 'symbols', label: 'Symbols', icon: Filter }
          ].map(({ key, label, icon: Icon }) => (
            <button
              key={key}
              onClick={() => setSearchType(key as SearchType)}
              className={`flex-1 flex items-center justify-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                searchType === key
                  ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              <Icon className="w-4 h-4" />
              <span>{label}</span>
            </button>
          ))}
        </div>

        {/* Search Input */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder={`Search ${searchType}...`}
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {loading && (
            <RefreshCw className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 animate-spin" />
          )}
        </div>

        {/* Results Count */}
        {query && !loading && (
          <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
            {getResultCount()} result{getResultCount() !== 1 ? 's' : ''} found
          </div>
        )}
      </div>

      {/* Results */}
      <div className="flex-1 overflow-auto p-4">
        {error ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-red-600 dark:text-red-400">
              <AlertCircle className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">Search failed</p>
              <p className="text-sm">{error}</p>
            </div>
          </div>
        ) : !query ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500 dark:text-gray-400">
              <Search className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">Start searching</p>
              <p className="text-sm">Enter a search query to find {searchType}</p>
            </div>
          </div>
        ) : loading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500 dark:text-gray-400">
              <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4" />
              <p>Searching...</p>
            </div>
          </div>
        ) : getResultCount() === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500 dark:text-gray-400">
              <Search className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">No results found</p>
              <p className="text-sm">Try a different search query</p>
            </div>
          </div>
        ) : (
          <div>
            {searchType === 'text' && renderTextResults()}
            {searchType === 'files' && renderFileResults()}
            {searchType === 'symbols' && renderSymbolResults()}
          </div>
        )}
      </div>
    </div>
  );
}
