'use client';

import { useState } from 'react';
import { MessageSquare, Plus, Trash2, Edit2, Check, X, FolderOpen, Calendar } from 'lucide-react';

interface Session {
  id: string;
  title: string;
  directory: string;
  version: string;
  projectID: string;
  time: {
    created: number;
    updated: number;
  };
}

interface SessionListProps {
  sessions: Session[];
  currentSessionId?: string;
  onSessionSelect: (sessionId: string) => void;
  onSessionCreate: (title: string) => Promise<void>;
  onSessionDelete: (sessionId: string) => Promise<void>;
  onSessionUpdate: (sessionId: string, title: string) => Promise<void>;
  isLoading?: boolean;
}

export function SessionList({
  sessions,
  currentSessionId,
  onSessionSelect,
  onSessionCreate,
  onSessionDelete,
  onSessionUpdate,
  isLoading = false
}: SessionListProps) {
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editTitle, setEditTitle] = useState('');
  const [newSessionTitle, setNewSessionTitle] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDirectory = (directory: string) => {
    // Show only the last part of the directory path
    const parts = directory.split('/');
    return parts[parts.length - 1] || directory;
  };

  const handleCreateSession = async () => {
    if (!newSessionTitle.trim()) return;
    
    try {
      await onSessionCreate(newSessionTitle.trim());
      setNewSessionTitle('');
      setIsCreating(false);
    } catch (error) {
      console.error('Failed to create session:', error);
    }
  };

  const handleUpdateSession = async (sessionId: string) => {
    if (!editTitle.trim()) return;
    
    try {
      await onSessionUpdate(sessionId, editTitle.trim());
      setEditingId(null);
      setEditTitle('');
    } catch (error) {
      console.error('Failed to update session:', error);
    }
  };

  const handleDeleteSession = async (sessionId: string) => {
    if (confirm('Are you sure you want to delete this session? This action cannot be undone.')) {
      try {
        await onSessionDelete(sessionId);
      } catch (error) {
        console.error('Failed to delete session:', error);
      }
    }
  };

  const startEditing = (session: Session) => {
    setEditingId(session.id);
    setEditTitle(session.title);
  };

  const cancelEditing = () => {
    setEditingId(null);
    setEditTitle('');
  };

  const sortedSessions = [...sessions].sort((a, b) => b.time.updated - a.time.updated);

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <h2 className="font-semibold text-gray-900 dark:text-white flex items-center gap-2">
          <MessageSquare className="w-4 h-4" />
          Sessions ({sessions.length})
        </h2>
        <button
          onClick={() => setIsCreating(true)}
          disabled={isLoading}
          className="p-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded disabled:opacity-50"
          aria-label="New session"
        >
          <Plus className="w-4 h-4" />
        </button>
      </div>

      {/* New session form */}
      {isCreating && (
        <div className="p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
          <div className="space-y-2">
            <input
              type="text"
              value={newSessionTitle}
              onChange={(e) => setNewSessionTitle(e.target.value)}
              placeholder="Session title..."
              className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              autoFocus
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleCreateSession();
                if (e.key === 'Escape') setIsCreating(false);
              }}
            />
            <div className="flex gap-2">
              <button
                onClick={handleCreateSession}
                disabled={!newSessionTitle.trim()}
                className="flex items-center gap-1 px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                <Check className="w-3 h-3" />
                Create
              </button>
              <button
                onClick={() => setIsCreating(false)}
                className="flex items-center gap-1 px-3 py-1 text-sm bg-gray-600 text-white rounded-md hover:bg-gray-700"
              >
                <X className="w-3 h-3" />
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Session list */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            Loading sessions...
          </div>
        ) : sessions.length === 0 ? (
          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
            <MessageSquare className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>No sessions yet</p>
            <p className="text-xs">Create your first session to get started</p>
          </div>
        ) : (
          <div className="space-y-1 p-2">
            {sortedSessions.map((session) => (
              <div
                key={session.id}
                className={`group relative p-3 rounded-md transition-colors cursor-pointer ${
                  currentSessionId === session.id
                    ? 'bg-blue-100 dark:bg-blue-900 text-blue-900 dark:text-blue-100'
                    : 'hover:bg-gray-100 dark:hover:bg-gray-800'
                }`}
                onClick={() => onSessionSelect(session.id)}
              >
                {editingId === session.id ? (
                  <div className="space-y-2" onClick={(e) => e.stopPropagation()}>
                    <input
                      type="text"
                      value={editTitle}
                      onChange={(e) => setEditTitle(e.target.value)}
                      className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                      autoFocus
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') handleUpdateSession(session.id);
                        if (e.key === 'Escape') cancelEditing();
                      }}
                    />
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleUpdateSession(session.id)}
                        className="flex items-center gap-1 px-2 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700"
                      >
                        <Check className="w-3 h-3" />
                        Save
                      </button>
                      <button
                        onClick={cancelEditing}
                        className="flex items-center gap-1 px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700"
                      >
                        <X className="w-3 h-3" />
                        Cancel
                      </button>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm truncate">
                          {session.title}
                        </div>
                        <div className="flex items-center gap-2 mt-1 text-xs text-gray-500 dark:text-gray-400">
                          <FolderOpen className="w-3 h-3" />
                          <span className="truncate">{formatDirectory(session.directory)}</span>
                        </div>
                        <div className="flex items-center gap-2 mt-1 text-xs text-gray-500 dark:text-gray-400">
                          <Calendar className="w-3 h-3" />
                          <span>{formatDate(session.time.updated)}</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            startEditing(session);
                          }}
                          className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                          aria-label="Edit session"
                        >
                          <Edit2 className="w-3 h-3" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteSession(session.id);
                          }}
                          className="p-1 hover:bg-red-200 dark:hover:bg-red-900 text-red-600 dark:text-red-400 rounded"
                          aria-label="Delete session"
                        >
                          <Trash2 className="w-3 h-3" />
                        </button>
                      </div>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
