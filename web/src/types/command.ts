/**
 * Command System Types
 * Types and interfaces for the OpenCode command system
 */

export interface Command {
  name: string;
  description?: string;
  agent?: string;
  model?: string;
  template: string;
  subtask?: boolean;
}

export interface CommandInput {
  sessionID: string;
  messageID?: string;
  agent?: string;
  model?: string;
  command: string;
  arguments: string;
}

export interface ShellInput {
  sessionID: string;
  agent: string;
  command: string;
}

export interface CommandResult {
  info: any; // AssistantMessage
  parts: any[]; // Part[]
}

export interface ShellResult {
  info: any; // AssistantMessage
}

export interface CommandHistoryItem {
  id: string;
  command: string;
  timestamp: Date;
  type: 'command' | 'shell';
  result?: CommandResult | ShellResult;
  error?: string;
}

export interface CommandSuggestion {
  name: string;
  description?: string;
  template: string;
  agent?: string;
  model?: string;
  score?: number; // For search ranking
}

export interface ShellSession {
  id: string;
  sessionId: string;
  agent: string;
  history: CommandHistoryItem[];
  isActive: boolean;
  workingDirectory?: string;
}

export interface CommandPaletteState {
  isOpen: boolean;
  query: string;
  selectedIndex: number;
  suggestions: CommandSuggestion[];
  history: CommandHistoryItem[];
  mode: 'command' | 'shell';
}

export interface CommandInputState {
  value: string;
  isValid: boolean;
  suggestions: CommandSuggestion[];
  selectedSuggestion: number;
  history: string[];
  historyIndex: number;
}

export interface ShellInterfaceState {
  sessions: ShellSession[];
  activeSessionId?: string;
  input: string;
  isExecuting: boolean;
  output: string[];
}

// Command parsing utilities
export interface ParsedCommand {
  command: string;
  arguments: string[];
  flags: Record<string, string | boolean>;
  raw: string;
}

// Built-in command types
export type BuiltInCommand = 
  | 'help'
  | 'clear'
  | 'history'
  | 'exit'
  | 'init'
  | 'status'
  | 'sessions'
  | 'agents'
  | 'models'
  | 'files'
  | 'search';

export interface BuiltInCommandDefinition {
  name: BuiltInCommand;
  description: string;
  usage: string;
  examples: string[];
  handler: (args: string[], context: CommandContext) => Promise<CommandResult>;
}

export interface CommandContext {
  sessionId: string;
  currentAgent?: string;
  currentModel?: string;
  workingDirectory?: string;
  apiService: any; // ApiService instance
}

// Command execution modes
export type CommandMode = 'interactive' | 'batch' | 'background';

export interface CommandExecution {
  id: string;
  command: ParsedCommand;
  mode: CommandMode;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  startTime: Date;
  endTime?: Date;
  result?: CommandResult | ShellResult;
  error?: string;
  progress?: number; // 0-100 for long-running commands
}

// Keyboard shortcuts for command system
export interface CommandKeyBinding {
  key: string;
  ctrlKey?: boolean;
  shiftKey?: boolean;
  altKey?: boolean;
  metaKey?: boolean;
  action: 'open-palette' | 'open-shell' | 'execute' | 'cancel' | 'history-up' | 'history-down';
}

export const DEFAULT_COMMAND_KEYBINDINGS: CommandKeyBinding[] = [
  { key: 'k', ctrlKey: true, action: 'open-palette' },
  { key: '`', ctrlKey: true, action: 'open-shell' },
  { key: 'Enter', action: 'execute' },
  { key: 'Escape', action: 'cancel' },
  { key: 'ArrowUp', action: 'history-up' },
  { key: 'ArrowDown', action: 'history-down' },
];

// Command validation
export interface CommandValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

// Command autocomplete
export interface AutocompleteResult {
  suggestions: CommandSuggestion[];
  hasMore: boolean;
  query: string;
}


