'use client';

import { useState } from 'react';
import { MessageInput } from '@/components/chat/message-input';

export default function TestCommandsPage() {
  const [messages, setMessages] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const handleSendMessage = async (message: string) => {
    setMessages(prev => [...prev, `Message: ${message}`]);
  };

  const handleExecuteCommand = async (command: string, args: string) => {
    setMessages(prev => [...prev, `Command: /${command} ${args}`]);
  };

  const handleStop = () => {
    setIsLoading(false);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          Command Input Test
        </h1>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Instructions
          </h2>
          <div className="text-gray-700 dark:text-gray-300 space-y-2">
            <p>• Type <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">/</code> to enter command mode</p>
            <p>• Use arrow keys (↑↓) to navigate suggestions</p>
            <p>• Press Tab or Enter to select a command</p>
            <p>• Press Escape to exit command mode</p>
            <p>• Type <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">/mo</code> to see filtered commands starting with "mo"</p>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg mb-6">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Messages & Commands
            </h3>
          </div>
          <div className="p-4 max-h-64 overflow-y-auto">
            {messages.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400 italic">
                No messages yet. Try typing a message or command below.
              </p>
            ) : (
              <div className="space-y-2">
                {messages.map((msg, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg ${
                      msg.startsWith('Command:')
                        ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
                        : 'bg-gray-50 dark:bg-gray-700'
                    }`}
                  >
                    <code className="text-sm font-mono text-gray-900 dark:text-white">
                      {msg}
                    </code>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg">
          <MessageInput
            onSendMessage={handleSendMessage}
            onExecuteCommand={handleExecuteCommand}
            isLoading={isLoading}
            onStop={handleStop}
            disabled={false}
            placeholder="Type a message or use / for commands..."
            sessionId="test-session"
          />
        </div>
      </div>
    </div>
  );
}
