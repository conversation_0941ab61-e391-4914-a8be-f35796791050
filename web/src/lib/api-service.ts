/**
 * API Service Layer for OpenCode Web UI
 * Provides a clean interface for interacting with the OpenCode server
 */

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface ConnectionConfig {
  baseUrl: string;
  timeout?: number;
}

export interface FileNode {
  name: string;
  path: string;
  absolute: string;
  type: 'file' | 'directory';
  ignored: boolean;
}

export interface FileContent {
  content: string;
  diff?: string;
  patch?: any;
}

export interface SearchMatch {
  path: { text: string };
  lines: { text: string };
  line_number: number;
  absolute_offset: number;
  submatches: Array<{
    match: { text: string };
    start: number;
    end: number;
  }>;
}

export interface Symbol {
  name: string;
  kind: number;
  location: {
    uri: string;
    range: {
      start: { line: number; character: number };
      end: { line: number; character: number };
    };
  };
}

export interface FileStatus {
  path: string;
  added: number;
  removed: number;
  status: 'added' | 'deleted' | 'modified';
}

export interface Command {
  name: string;
  description?: string;
  agent?: string;
  model?: string;
  template: string;
  subtask?: boolean;
}

export interface CommandInput {
  sessionID: string;
  messageID?: string;
  agent?: string;
  model?: string;
  command: string;
  arguments: string;
}

export interface ShellInput {
  sessionID: string;
  agent: string;
  command: string;
}

export interface CommandResult {
  info: any; // AssistantMessage
  parts: any[]; // Part[]
}

export interface ShellResult {
  info: any; // AssistantMessage
}

export class ApiService {
  private config: ConnectionConfig;

  constructor(config: ConnectionConfig) {
    this.config = {
      timeout: 10000, // 10 seconds default
      ...config
    };
  }

  /**
   * Update the base URL for the API service
   */
  updateBaseUrl(baseUrl: string) {
    this.config.baseUrl = baseUrl.replace(/\/$/, ''); // Remove trailing slash
  }

  /**
   * Make a request through the proxy
   */
  private async makeRequest<T>(endpoint: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
    try {
      const url = `/api/proxy?baseUrl=${encodeURIComponent(this.config.baseUrl)}&endpoint=${encodeURIComponent(endpoint)}`;
      
      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      return {
        success: true,
        data
      };
    } catch (error) {
      console.error('API request failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Test connection to the server
   */
  async testConnection(): Promise<ApiResponse<any>> {
    return this.makeRequest('/config');
  }

  /**
   * Get server configuration
   */
  async getConfig(): Promise<ApiResponse<any>> {
    return this.makeRequest('/config');
  }

  /**
   * Get all sessions
   */
  async getSessions(): Promise<ApiResponse<any[]>> {
    return this.makeRequest('/session');
  }

  /**
   * Get a specific session
   */
  async getSession(sessionId: string): Promise<ApiResponse<any>> {
    return this.makeRequest(`/session/${sessionId}`);
  }

  /**
   * Create a new session
   */
  async createSession(data: { parentID?: string; title?: string }): Promise<ApiResponse<any>> {
    return this.makeRequest('/session', {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  /**
   * Delete a session
   */
  async deleteSession(sessionId: string): Promise<ApiResponse<boolean>> {
    return this.makeRequest(`/session/${sessionId}`, {
      method: 'DELETE'
    });
  }



  /**
   * Update a session
   */
  async updateSession(sessionId: string, data: { title?: string }): Promise<ApiResponse<any>> {
    return this.makeRequest(`/session/${sessionId}`, {
      method: 'PATCH',
      body: JSON.stringify(data)
    });
  }

  /**
   * Get messages for a session
   */
  async getSessionMessages(sessionId: string): Promise<ApiResponse<any[]>> {
    return this.makeRequest(`/session/${sessionId}/message`);
  }

  /**
   * Send a message to a session
   */
  async sendMessage(sessionId: string, message: string): Promise<ApiResponse<any>> {
    return this.makeRequest(`/session/${sessionId}/message`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        parts: [
          {
            type: 'text',
            text: message
          }
        ]
      })
    });
  }



  /**
   * List files and directories
   */
  async listFiles(path: string = '.'): Promise<FileNode[]> {
    const response = await this.makeRequest<FileNode[]>(`/file?path=${encodeURIComponent(path)}`);
    if (!response.success) {
      throw new Error(response.error || 'Failed to list files');
    }
    return response.data || [];
  }

  /**
   * Read file content
   */
  async readFile(path: string): Promise<FileContent> {
    const response = await this.makeRequest<FileContent>(`/file/content?path=${encodeURIComponent(path)}`);
    if (!response.success) {
      throw new Error(response.error || 'Failed to read file');
    }
    return response.data || { content: '' };
  }

  /**
   * Search for text in files
   */
  async searchText(pattern: string): Promise<SearchMatch[]> {
    const response = await this.makeRequest<SearchMatch[]>(`/find?pattern=${encodeURIComponent(pattern)}`);
    if (!response.success) {
      throw new Error(response.error || 'Failed to search text');
    }
    return response.data || [];
  }

  /**
   * Search for files by name
   */
  async searchFiles(query: string): Promise<string[]> {
    const response = await this.makeRequest<string[]>(`/find/file?query=${encodeURIComponent(query)}`);
    if (!response.success) {
      throw new Error(response.error || 'Failed to search files');
    }
    return response.data || [];
  }

  /**
   * Search for workspace symbols
   */
  async searchSymbols(query: string): Promise<Symbol[]> {
    const response = await this.makeRequest<Symbol[]>(`/find/symbol?query=${encodeURIComponent(query)}`);
    if (!response.success) {
      throw new Error(response.error || 'Failed to search symbols');
    }
    return response.data || [];
  }

  /**
   * Get file status (git status)
   */
  async getFileStatus(): Promise<FileStatus[]> {
    const response = await this.makeRequest<FileStatus[]>('/file/status');
    if (!response.success) {
      throw new Error(response.error || 'Failed to get file status');
    }
    return response.data || [];
  }

  /**
   * Get available agents
   */
  async getAgents(): Promise<ApiResponse<any[]>> {
    return this.makeRequest<any[]>('/agent');
  }

  /**
   * Get available providers and models
   */
  async getProviders(): Promise<ApiResponse<any>> {
    return this.makeRequest<any>('/config/providers');
  }

  /**
   * Get available commands
   */
  async getCommands(): Promise<ApiResponse<Command[]>> {
    return this.makeRequest<Command[]>('/command');
  }

  /**
   * Execute a command in a session
   */
  async executeCommand(sessionId: string, commandData: Omit<CommandInput, 'sessionID'>): Promise<ApiResponse<CommandResult>> {
    return this.makeRequest<CommandResult>(`/session/${sessionId}/command`, {
      method: 'POST',
      body: JSON.stringify(commandData)
    });
  }

  /**
   * Execute a shell command in a session
   */
  async executeShell(sessionId: string, shellData: Omit<ShellInput, 'sessionID'>): Promise<ApiResponse<ShellResult>> {
    return this.makeRequest<ShellResult>(`/session/${sessionId}/shell`, {
      method: 'POST',
      body: JSON.stringify(shellData)
    });
  }

  /**
   * Revert a message in a session
   */
  async revertMessage(sessionId: string, messageId: string, partId?: string): Promise<ApiResponse<any>> {
    return this.makeRequest(`/session/${sessionId}/revert`, {
      method: 'POST',
      body: JSON.stringify({ messageID: messageId, partID: partId })
    });
  }
}

// Default API service instance
export const apiService = new ApiService({
  baseUrl: 'http://127.0.0.1:40167'
});

// Helper function to update the global API service
export function updateApiService(baseUrl: string) {
  apiService.updateBaseUrl(baseUrl);
  return apiService;
}
