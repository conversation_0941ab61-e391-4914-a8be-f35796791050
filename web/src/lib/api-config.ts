import { createClient, createConfig } from './api-client/client';
import type { ClientOptions } from './api-client/types.gen';

// Global API client instance
let apiClient: ReturnType<typeof createClient> | null = null;

// Default configuration
const DEFAULT_BASE_URL = 'http://127.0.0.1:40167';

/**
 * Get the current API client instance
 */
export function getApiClient() {
  if (!apiClient) {
    apiClient = createClient(createConfig<ClientOptions>({
      baseUrl: DEFAULT_BASE_URL
    }));
  }
  return apiClient;
}

/**
 * Update the API client with a new base URL
 */
export function updateApiClient(baseUrl: string) {
  apiClient = createClient(createConfig<ClientOptions>({
    baseUrl: baseUrl.replace(/\/$/, '') // Remove trailing slash
  }));
  return apiClient;
}

/**
 * Test connection to the API server
 */
export async function testConnection(baseUrl?: string): Promise<{ success: boolean; error?: string; data?: any }> {
  try {
    const client = baseUrl ? updateApiClient(baseUrl) : getApiClient();
    
    // Test with a simple endpoint
    const response = await client.get({
      url: '/config'
    });
    
    return {
      success: true,
      data: response.data
    };
  } catch (error) {
    console.error('Connection test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Get the current base URL from the client
 */
export function getCurrentBaseUrl(): string {
  return DEFAULT_BASE_URL;
}
