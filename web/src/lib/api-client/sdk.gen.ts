// This file is auto-generated by @hey-api/openapi-ts

import type { Options as ClientOptions, Client, TDataShape } from './client';
import type { ProjectListData, ProjectListResponses, ProjectCurrentData, ProjectCurrentResponses, ConfigGetData, ConfigGetResponses, ToolIdsData, ToolIdsResponses, ToolIdsErrors, ToolListData, ToolListResponses, ToolListErrors, PathGetData, PathGetResponses, SessionListData, SessionListResponses, SessionCreateData, SessionCreateResponses, SessionCreateErrors, SessionDeleteData, SessionDeleteResponses, SessionGetData, SessionGetResponses, SessionUpdateData, SessionUpdateResponses, SessionChildrenData, SessionChildrenResponses, SessionInitData, SessionInitResponses, SessionAbortData, SessionAbortResponses, SessionUnshareData, SessionUnshareResponses, SessionShareData, SessionShareResponses, SessionSummarizeData, SessionSummarizeResponses, SessionMessagesData, SessionMessagesResponses, SessionPromptData, SessionPromptResponses, SessionMessageData, SessionMessageResponses, SessionCommandData, SessionCommandResponses, SessionShellData, SessionShellResponses, SessionRevertData, SessionRevertResponses, SessionUnrevertData, SessionUnrevertResponses, PostSessionIdPermissionsPermissionIdData, PostSessionIdPermissionsPermissionIdResponses, CommandListData, CommandListResponses, ConfigProvidersData, ConfigProvidersResponses, FindTextData, FindTextResponses, FindFilesData, FindFilesResponses, FindSymbolsData, FindSymbolsResponses, FileListData, FileListResponses, FileReadData, FileReadResponses, FileStatusData, FileStatusResponses, AppLogData, AppLogResponses, AppAgentsData, AppAgentsResponses, TuiAppendPromptData, TuiAppendPromptResponses, TuiOpenHelpData, TuiOpenHelpResponses, TuiOpenSessionsData, TuiOpenSessionsResponses, TuiOpenThemesData, TuiOpenThemesResponses, TuiOpenModelsData, TuiOpenModelsResponses, TuiSubmitPromptData, TuiSubmitPromptResponses, TuiClearPromptData, TuiClearPromptResponses, TuiExecuteCommandData, TuiExecuteCommandResponses, TuiShowToastData, TuiShowToastResponses, AuthSetData, AuthSetResponses, AuthSetErrors, EventSubscribeData, EventSubscribeResponses } from './types.gen';
import { client } from './client.gen';

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<TData, ThrowOnError> & {
    /**
     * You can provide a client instance returned by `createClient()` instead of
     * individual options. This might be also useful if you want to implement a
     * custom client.
     */
    client?: Client;
    /**
     * You can pass arbitrary values through the `meta` object. This can be
     * used to access values that aren't defined as part of the SDK function.
     */
    meta?: Record<string, unknown>;
};

/**
 * List all projects
 */
export const projectList = <ThrowOnError extends boolean = false>(options?: Options<ProjectListData, ThrowOnError>) => {
    return (options?.client ?? client).get<ProjectListResponses, unknown, ThrowOnError>({
        url: '/project',
        ...options
    });
};

/**
 * Get the current project
 */
export const projectCurrent = <ThrowOnError extends boolean = false>(options?: Options<ProjectCurrentData, ThrowOnError>) => {
    return (options?.client ?? client).get<ProjectCurrentResponses, unknown, ThrowOnError>({
        url: '/project/current',
        ...options
    });
};

/**
 * Get config info
 */
export const configGet = <ThrowOnError extends boolean = false>(options?: Options<ConfigGetData, ThrowOnError>) => {
    return (options?.client ?? client).get<ConfigGetResponses, unknown, ThrowOnError>({
        url: '/config',
        ...options
    });
};

/**
 * List all tool IDs (including built-in and dynamically registered)
 */
export const toolIds = <ThrowOnError extends boolean = false>(options?: Options<ToolIdsData, ThrowOnError>) => {
    return (options?.client ?? client).get<ToolIdsResponses, ToolIdsErrors, ThrowOnError>({
        url: '/experimental/tool/ids',
        ...options
    });
};

/**
 * List tools with JSON schema parameters for a provider/model
 */
export const toolList = <ThrowOnError extends boolean = false>(options: Options<ToolListData, ThrowOnError>) => {
    return (options.client ?? client).get<ToolListResponses, ToolListErrors, ThrowOnError>({
        url: '/experimental/tool',
        ...options
    });
};

/**
 * Get the current path
 */
export const pathGet = <ThrowOnError extends boolean = false>(options?: Options<PathGetData, ThrowOnError>) => {
    return (options?.client ?? client).get<PathGetResponses, unknown, ThrowOnError>({
        url: '/path',
        ...options
    });
};

/**
 * List all sessions
 */
export const sessionList = <ThrowOnError extends boolean = false>(options?: Options<SessionListData, ThrowOnError>) => {
    return (options?.client ?? client).get<SessionListResponses, unknown, ThrowOnError>({
        url: '/session',
        ...options
    });
};

/**
 * Create a new session
 */
export const sessionCreate = <ThrowOnError extends boolean = false>(options?: Options<SessionCreateData, ThrowOnError>) => {
    return (options?.client ?? client).post<SessionCreateResponses, SessionCreateErrors, ThrowOnError>({
        url: '/session',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Delete a session and all its data
 */
export const sessionDelete = <ThrowOnError extends boolean = false>(options: Options<SessionDeleteData, ThrowOnError>) => {
    return (options.client ?? client).delete<SessionDeleteResponses, unknown, ThrowOnError>({
        url: '/session/{id}',
        ...options
    });
};

/**
 * Get session
 */
export const sessionGet = <ThrowOnError extends boolean = false>(options: Options<SessionGetData, ThrowOnError>) => {
    return (options.client ?? client).get<SessionGetResponses, unknown, ThrowOnError>({
        url: '/session/{id}',
        ...options
    });
};

/**
 * Update session properties
 */
export const sessionUpdate = <ThrowOnError extends boolean = false>(options: Options<SessionUpdateData, ThrowOnError>) => {
    return (options.client ?? client).patch<SessionUpdateResponses, unknown, ThrowOnError>({
        url: '/session/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get a session's children
 */
export const sessionChildren = <ThrowOnError extends boolean = false>(options: Options<SessionChildrenData, ThrowOnError>) => {
    return (options.client ?? client).get<SessionChildrenResponses, unknown, ThrowOnError>({
        url: '/session/{id}/children',
        ...options
    });
};

/**
 * Analyze the app and create an AGENTS.md file
 */
export const sessionInit = <ThrowOnError extends boolean = false>(options: Options<SessionInitData, ThrowOnError>) => {
    return (options.client ?? client).post<SessionInitResponses, unknown, ThrowOnError>({
        url: '/session/{id}/init',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Abort a session
 */
export const sessionAbort = <ThrowOnError extends boolean = false>(options: Options<SessionAbortData, ThrowOnError>) => {
    return (options.client ?? client).post<SessionAbortResponses, unknown, ThrowOnError>({
        url: '/session/{id}/abort',
        ...options
    });
};

/**
 * Unshare the session
 */
export const sessionUnshare = <ThrowOnError extends boolean = false>(options: Options<SessionUnshareData, ThrowOnError>) => {
    return (options.client ?? client).delete<SessionUnshareResponses, unknown, ThrowOnError>({
        url: '/session/{id}/share',
        ...options
    });
};

/**
 * Share a session
 */
export const sessionShare = <ThrowOnError extends boolean = false>(options: Options<SessionShareData, ThrowOnError>) => {
    return (options.client ?? client).post<SessionShareResponses, unknown, ThrowOnError>({
        url: '/session/{id}/share',
        ...options
    });
};

/**
 * Summarize the session
 */
export const sessionSummarize = <ThrowOnError extends boolean = false>(options: Options<SessionSummarizeData, ThrowOnError>) => {
    return (options.client ?? client).post<SessionSummarizeResponses, unknown, ThrowOnError>({
        url: '/session/{id}/summarize',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * List messages for a session
 */
export const sessionMessages = <ThrowOnError extends boolean = false>(options: Options<SessionMessagesData, ThrowOnError>) => {
    return (options.client ?? client).get<SessionMessagesResponses, unknown, ThrowOnError>({
        url: '/session/{id}/message',
        ...options
    });
};

/**
 * Create and send a new message to a session
 */
export const sessionPrompt = <ThrowOnError extends boolean = false>(options: Options<SessionPromptData, ThrowOnError>) => {
    return (options.client ?? client).post<SessionPromptResponses, unknown, ThrowOnError>({
        url: '/session/{id}/message',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get a message from a session
 */
export const sessionMessage = <ThrowOnError extends boolean = false>(options: Options<SessionMessageData, ThrowOnError>) => {
    return (options.client ?? client).get<SessionMessageResponses, unknown, ThrowOnError>({
        url: '/session/{id}/message/{messageID}',
        ...options
    });
};

/**
 * Send a new command to a session
 */
export const sessionCommand = <ThrowOnError extends boolean = false>(options: Options<SessionCommandData, ThrowOnError>) => {
    return (options.client ?? client).post<SessionCommandResponses, unknown, ThrowOnError>({
        url: '/session/{id}/command',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Run a shell command
 */
export const sessionShell = <ThrowOnError extends boolean = false>(options: Options<SessionShellData, ThrowOnError>) => {
    return (options.client ?? client).post<SessionShellResponses, unknown, ThrowOnError>({
        url: '/session/{id}/shell',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Revert a message
 */
export const sessionRevert = <ThrowOnError extends boolean = false>(options: Options<SessionRevertData, ThrowOnError>) => {
    return (options.client ?? client).post<SessionRevertResponses, unknown, ThrowOnError>({
        url: '/session/{id}/revert',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Restore all reverted messages
 */
export const sessionUnrevert = <ThrowOnError extends boolean = false>(options: Options<SessionUnrevertData, ThrowOnError>) => {
    return (options.client ?? client).post<SessionUnrevertResponses, unknown, ThrowOnError>({
        url: '/session/{id}/unrevert',
        ...options
    });
};

/**
 * Respond to a permission request
 */
export const postSessionIdPermissionsPermissionId = <ThrowOnError extends boolean = false>(options: Options<PostSessionIdPermissionsPermissionIdData, ThrowOnError>) => {
    return (options.client ?? client).post<PostSessionIdPermissionsPermissionIdResponses, unknown, ThrowOnError>({
        url: '/session/{id}/permissions/{permissionID}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * List all commands
 */
export const commandList = <ThrowOnError extends boolean = false>(options?: Options<CommandListData, ThrowOnError>) => {
    return (options?.client ?? client).get<CommandListResponses, unknown, ThrowOnError>({
        url: '/command',
        ...options
    });
};

/**
 * List all providers
 */
export const configProviders = <ThrowOnError extends boolean = false>(options?: Options<ConfigProvidersData, ThrowOnError>) => {
    return (options?.client ?? client).get<ConfigProvidersResponses, unknown, ThrowOnError>({
        url: '/config/providers',
        ...options
    });
};

/**
 * Find text in files
 */
export const findText = <ThrowOnError extends boolean = false>(options: Options<FindTextData, ThrowOnError>) => {
    return (options.client ?? client).get<FindTextResponses, unknown, ThrowOnError>({
        url: '/find',
        ...options
    });
};

/**
 * Find files
 */
export const findFiles = <ThrowOnError extends boolean = false>(options: Options<FindFilesData, ThrowOnError>) => {
    return (options.client ?? client).get<FindFilesResponses, unknown, ThrowOnError>({
        url: '/find/file',
        ...options
    });
};

/**
 * Find workspace symbols
 */
export const findSymbols = <ThrowOnError extends boolean = false>(options: Options<FindSymbolsData, ThrowOnError>) => {
    return (options.client ?? client).get<FindSymbolsResponses, unknown, ThrowOnError>({
        url: '/find/symbol',
        ...options
    });
};

/**
 * List files and directories
 */
export const fileList = <ThrowOnError extends boolean = false>(options: Options<FileListData, ThrowOnError>) => {
    return (options.client ?? client).get<FileListResponses, unknown, ThrowOnError>({
        url: '/file',
        ...options
    });
};

/**
 * Read a file
 */
export const fileRead = <ThrowOnError extends boolean = false>(options: Options<FileReadData, ThrowOnError>) => {
    return (options.client ?? client).get<FileReadResponses, unknown, ThrowOnError>({
        url: '/file/content',
        ...options
    });
};

/**
 * Get file status
 */
export const fileStatus = <ThrowOnError extends boolean = false>(options?: Options<FileStatusData, ThrowOnError>) => {
    return (options?.client ?? client).get<FileStatusResponses, unknown, ThrowOnError>({
        url: '/file/status',
        ...options
    });
};

/**
 * Write a log entry to the server logs
 */
export const appLog = <ThrowOnError extends boolean = false>(options?: Options<AppLogData, ThrowOnError>) => {
    return (options?.client ?? client).post<AppLogResponses, unknown, ThrowOnError>({
        url: '/log',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * List all agents
 */
export const appAgents = <ThrowOnError extends boolean = false>(options?: Options<AppAgentsData, ThrowOnError>) => {
    return (options?.client ?? client).get<AppAgentsResponses, unknown, ThrowOnError>({
        url: '/agent',
        ...options
    });
};

/**
 * Append prompt to the TUI
 */
export const tuiAppendPrompt = <ThrowOnError extends boolean = false>(options?: Options<TuiAppendPromptData, ThrowOnError>) => {
    return (options?.client ?? client).post<TuiAppendPromptResponses, unknown, ThrowOnError>({
        url: '/tui/append-prompt',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Open the help dialog
 */
export const tuiOpenHelp = <ThrowOnError extends boolean = false>(options?: Options<TuiOpenHelpData, ThrowOnError>) => {
    return (options?.client ?? client).post<TuiOpenHelpResponses, unknown, ThrowOnError>({
        url: '/tui/open-help',
        ...options
    });
};

/**
 * Open the session dialog
 */
export const tuiOpenSessions = <ThrowOnError extends boolean = false>(options?: Options<TuiOpenSessionsData, ThrowOnError>) => {
    return (options?.client ?? client).post<TuiOpenSessionsResponses, unknown, ThrowOnError>({
        url: '/tui/open-sessions',
        ...options
    });
};

/**
 * Open the theme dialog
 */
export const tuiOpenThemes = <ThrowOnError extends boolean = false>(options?: Options<TuiOpenThemesData, ThrowOnError>) => {
    return (options?.client ?? client).post<TuiOpenThemesResponses, unknown, ThrowOnError>({
        url: '/tui/open-themes',
        ...options
    });
};

/**
 * Open the model dialog
 */
export const tuiOpenModels = <ThrowOnError extends boolean = false>(options?: Options<TuiOpenModelsData, ThrowOnError>) => {
    return (options?.client ?? client).post<TuiOpenModelsResponses, unknown, ThrowOnError>({
        url: '/tui/open-models',
        ...options
    });
};

/**
 * Submit the prompt
 */
export const tuiSubmitPrompt = <ThrowOnError extends boolean = false>(options?: Options<TuiSubmitPromptData, ThrowOnError>) => {
    return (options?.client ?? client).post<TuiSubmitPromptResponses, unknown, ThrowOnError>({
        url: '/tui/submit-prompt',
        ...options
    });
};

/**
 * Clear the prompt
 */
export const tuiClearPrompt = <ThrowOnError extends boolean = false>(options?: Options<TuiClearPromptData, ThrowOnError>) => {
    return (options?.client ?? client).post<TuiClearPromptResponses, unknown, ThrowOnError>({
        url: '/tui/clear-prompt',
        ...options
    });
};

/**
 * Execute a TUI command (e.g. agent_cycle)
 */
export const tuiExecuteCommand = <ThrowOnError extends boolean = false>(options?: Options<TuiExecuteCommandData, ThrowOnError>) => {
    return (options?.client ?? client).post<TuiExecuteCommandResponses, unknown, ThrowOnError>({
        url: '/tui/execute-command',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Show a toast notification in the TUI
 */
export const tuiShowToast = <ThrowOnError extends boolean = false>(options?: Options<TuiShowToastData, ThrowOnError>) => {
    return (options?.client ?? client).post<TuiShowToastResponses, unknown, ThrowOnError>({
        url: '/tui/show-toast',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options?.headers
        }
    });
};

/**
 * Set authentication credentials
 */
export const authSet = <ThrowOnError extends boolean = false>(options: Options<AuthSetData, ThrowOnError>) => {
    return (options.client ?? client).put<AuthSetResponses, AuthSetErrors, ThrowOnError>({
        url: '/auth/{id}',
        ...options,
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        }
    });
};

/**
 * Get events
 */
export const eventSubscribe = <ThrowOnError extends boolean = false>(options?: Options<EventSubscribeData, ThrowOnError>) => {
    return (options?.client ?? client).sse.get<EventSubscribeResponses, unknown, ThrowOnError>({
        url: '/event',
        ...options
    });
};
