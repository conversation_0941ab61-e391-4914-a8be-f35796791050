# API Endpoints Reference

Complete reference of all OpenCode API endpoints that the Web UI needs to interact with.

## Base Configuration

- **Base URL**: `http://localhost:PORT` (configurable)
- **Content-Type**: `application/json`
- **Query Parameter**: `directory` (optional) - working directory path

## Core Endpoints

### Configuration & Info

#### GET `/config`
Get configuration information
- **Response**: Configuration object with providers, models, etc.

#### GET `/path`
Get current paths (state, config, worktree, directory)
- **Response**: `{ state: string, config: string, worktree: string, directory: string }`

#### GET `/config/providers`
List all available providers and models
- **Response**: `{ providers: Provider[], default: Record<string, string> }`

### Session Management

#### GET `/session`
List all sessions
- **Response**: `Session[]` (sorted by updated time, newest first)

#### POST `/session`
Create a new session
- **Body**: `{ parentID?: string, title?: string }`
- **Response**: `Session`

#### GET `/session/:id`
Get specific session details
- **Response**: `Session`

#### PATCH `/session/:id`
Update session properties
- **Body**: `{ title?: string }`
- **Response**: `Session`

#### DELETE `/session/:id`
Delete a session and all its data
- **Response**: `boolean`

#### GET `/session/:id/children`
Get session's child sessions
- **Response**: `Session[]`

#### POST `/session/:id/init`
Initialize session (analyze app and create AGENTS.md)
- **Body**: `{ messageID: string, providerID: string, modelID: string }`
- **Response**: `boolean`

#### POST `/session/:id/abort`
Abort current session processing
- **Response**: `boolean`

#### POST `/session/:id/share`
Share a session
- **Response**: `Session` (with share URL)

#### DELETE `/session/:id/share`
Unshare a session
- **Response**: `Session`

#### POST `/session/:id/summarize`
Summarize the session
- **Body**: `{ providerID: string, modelID: string }`
- **Response**: `boolean`

### Message Management

#### GET `/session/:id/message`
List messages for a session
- **Response**: `MessageWithParts[]`

#### GET `/session/:id/message/:messageID`
Get specific message
- **Response**: `{ info: Message, parts: Part[] }`

#### POST `/session/:id/message`
Send a new message/prompt
- **Body**: `PromptInput` (without sessionID)
- **Response**: `{ info: AssistantMessage, parts: Part[] }`

#### POST `/session/:id/command`
Execute a command
- **Body**: `{ messageID?: string, agent?: string, model?: string, arguments: string, command: string }`
- **Response**: `{ info: AssistantMessage, parts: Part[] }`

#### POST `/session/:id/shell`
Execute shell command
- **Body**: `{ agent: string, command: string }`
- **Response**: `AssistantMessage`

#### POST `/session/:id/revert`
Revert a message
- **Body**: `{ messageID: string, partID?: string }`
- **Response**: `Session`

#### POST `/session/:id/unrevert`
Restore all reverted messages
- **Response**: `Session`

### Permission Management

#### POST `/session/:id/permissions/:permissionID`
Respond to permission request
- **Body**: `{ response: "allow" | "deny" | "always" | "never" }`
- **Response**: `boolean`

### File Operations

#### GET `/file`
List files and directories
- **Query**: `path: string`
- **Response**: `FileNode[]`

#### GET `/file/content`
Read file content
- **Query**: `path: string`
- **Response**: `FileContent`

#### GET `/file/status`
Get file status (git status)
- **Response**: `FileInfo[]`

### Search Operations

#### GET `/find`
Search text in files
- **Query**: `pattern: string`
- **Response**: `SearchMatch[]`

#### GET `/find/file`
Find files by name
- **Query**: `query: string`
- **Response**: `string[]` (file paths)

#### GET `/find/symbol`
Find workspace symbols
- **Query**: `query: string`
- **Response**: `Symbol[]`

### Agent & Command Management

#### GET `/agent`
List all available agents
- **Response**: `Agent[]`

#### GET `/command`
List all available commands
- **Response**: `Command[]`

### Tool Management

#### GET `/experimental/tool/ids`
List all tool IDs
- **Response**: `string[]`

#### GET `/experimental/tool`
List tools with schemas
- **Query**: `provider: string, model: string`
- **Response**: `ToolListItem[]`

### Authentication

#### PUT `/auth/:id`
Set authentication credentials
- **Body**: `AuthInfo`
- **Response**: `boolean`

### Real-time Events

#### GET `/event`
Server-Sent Events stream
- **Response**: Event stream with various event types
- **Events**: 
  - `server.connected`
  - `message.updated`
  - `message.part.updated`
  - `session.updated`
  - `file.watcher.updated`
  - etc.

### TUI Control (for integration)

#### POST `/tui/append-prompt`
Append text to TUI prompt
- **Body**: `{ text: string }`

#### POST `/tui/submit-prompt`
Submit current TUI prompt

#### POST `/tui/clear-prompt`
Clear TUI prompt

#### POST `/tui/execute-command`
Execute TUI command
- **Body**: `{ command: string }`

#### POST `/tui/show-toast`
Show toast notification
- **Body**: `{ title?: string, message: string, variant: "info" | "success" | "warning" | "error" }`

#### POST `/tui/open-*`
Open various TUI dialogs (help, sessions, themes, models)

### Logging

#### POST `/log`
Write log entry
- **Body**: `{ service: string, level: "debug" | "info" | "error" | "warn", message: string, extra?: Record<string, any> }`
- **Response**: `boolean`

## Error Handling

All endpoints may return 400 errors with this format:
```json
{
  "data": {
    "name": "ErrorType",
    "data": { /* error-specific data */ }
  }
}
```

Common error types:
- `ProviderAuthError`
- `UnknownError`
- `MessageOutputLengthError`
- `AbortedError`

## Authentication

Some providers require authentication. Use the `/auth/:id` endpoint to set credentials before making requests that require them.
