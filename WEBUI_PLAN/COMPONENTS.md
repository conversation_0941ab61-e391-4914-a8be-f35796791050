# UI Components Structure

Complete breakdown of all UI components needed for the OpenCode Web UI, organized by functionality and hierarchy.

## Application Layout

### App Shell
```
┌─────────────────────────────────────────────────────────────┐
│ Header (AppHeader)                                          │
├─────────────────┬───────────────────────────────────────────┤
│ Sidebar         │ Main Content Area                         │
│ (AppSidebar)    │ (MainContent)                            │
│                 │                                           │
│ - Sessions      │ ┌─────────────────────────────────────┐   │
│ - Files         │ │ Chat Interface (ChatContainer)      │   │
│ - Settings      │ │                                     │   │
│                 │ │ - Messages (MessageList)            │   │
│                 │ │ - Input (MessageInput)              │   │
│                 │ └─────────────────────────────────────┘   │
└─────────────────┴───────────────────────────────────────────┘
```

### Core Layout Components

#### AppHeader
- **Purpose**: Top navigation and global controls
- **Features**:
  - OpenCode logo and title
  - Connection status indicator
  - Current session title (editable)
  - Agent/model selector
  - Global actions (share, settings)
- **State**: Current session, connection status, selected agent/model

#### AppSidebar
- **Purpose**: Navigation and secondary content
- **Features**:
  - Collapsible/expandable
  - Tab-based navigation (Sessions, Files, Settings)
  - Resizable width
- **State**: Open/closed, active tab, width

#### MainContent
- **Purpose**: Primary content area
- **Features**:
  - Responsive layout
  - Context-aware content (chat, file viewer, settings)
- **State**: Current view mode

## Session Management Components

### SessionList
- **Purpose**: Display and manage sessions
- **Features**:
  - Hierarchical session tree (parent/child)
  - Session search and filtering
  - Create new session button
  - Session actions (delete, share, rename)
  - Current session highlighting
- **State**: Sessions list, selected session, search query

### SessionItem
- **Purpose**: Individual session in the list
- **Features**:
  - Session title (editable inline)
  - Last updated time
  - Share status indicator
  - Context menu (delete, share, rename)
  - Child session indicators
- **State**: Session data, edit mode, menu open

### SessionDialog
- **Purpose**: Session creation and management
- **Features**:
  - Create new session form
  - Parent session selection
  - Session title input
  - Agent/model selection for new session
- **State**: Form data, validation errors

## Chat Interface Components

### ChatContainer
- **Purpose**: Main chat interface wrapper
- **Features**:
  - Message list with virtual scrolling
  - Message input area
  - Loading states
  - Auto-scroll to bottom
- **State**: Messages, loading, scroll position

### MessageList
- **Purpose**: Display conversation messages
- **Features**:
  - Virtual scrolling for performance
  - Message grouping by role
  - Timestamp display
  - Message selection and copying
  - Syntax highlighting for code
- **State**: Messages, selection, scroll position

### MessageItem
- **Purpose**: Individual message display
- **Features**:
  - Role-based styling (user/assistant)
  - Message parts rendering
  - Timestamp and metadata
  - Copy message button
  - Revert/undo actions
- **State**: Message data, expanded state

### MessageParts
- **Purpose**: Render different message part types
- **Components**:
  - **TextPart**: Plain text with markdown
  - **CodePart**: Syntax-highlighted code blocks
  - **ToolInvocationPart**: Tool execution display
  - **FilePart**: File content display
  - **ReasoningPart**: AI reasoning display
  - **StepPart**: Step indicators
- **State**: Part data, expanded state

### MessageInput
- **Purpose**: Message composition and sending
- **Features**:
  - Multi-line text input with auto-resize
  - File attachment support
  - Command autocomplete
  - Send button and keyboard shortcuts
  - Input history navigation
  - Agent/model quick selection
- **State**: Input text, attachments, history position

### ToolExecutionDisplay
- **Purpose**: Show tool execution progress and results
- **Features**:
  - Real-time execution status
  - Output streaming
  - Error display
  - Collapsible output
  - Copy output button
- **State**: Execution status, output, expanded state

## File Management Components

### FileExplorer
- **Purpose**: File system navigation
- **Features**:
  - Tree view of directories and files
  - File type icons
  - Git status indicators
  - Context menu actions
  - Search and filtering
- **State**: File tree, selected file, expanded directories

### FileItem
- **Purpose**: Individual file/directory in explorer
- **Features**:
  - File/folder icon
  - Name with git status
  - Context menu (open, edit, delete, etc.)
  - Drag and drop support
- **State**: File data, selected state, menu open

### FileViewer
- **Purpose**: Display file content
- **Features**:
  - Syntax highlighting
  - Line numbers
  - Git diff display
  - Edit mode toggle
  - Copy content button
- **State**: File content, edit mode, diff view

### FileEditor
- **Purpose**: Edit file content
- **Features**:
  - Monaco editor integration
  - Syntax highlighting
  - Auto-completion
  - Save/cancel actions
  - Diff preview
- **State**: File content, dirty state, validation

### SearchInterface
- **Purpose**: File and content search
- **Features**:
  - Text search input
  - File name search
  - Symbol search
  - Search results display
  - Filter options
- **State**: Search query, results, filters

## Agent & Model Management

### AgentSelector
- **Purpose**: Select and configure agents
- **Features**:
  - Agent dropdown/modal
  - Agent descriptions
  - Recently used agents
  - Custom agent creation
- **State**: Available agents, selected agent, recent agents

### ModelSelector
- **Purpose**: Select AI model and provider
- **Features**:
  - Provider/model dropdown
  - Model capabilities display
  - Cost information
  - Recently used models
- **State**: Available models, selected model, recent models

### AgentDialog
- **Purpose**: Agent configuration and creation
- **Features**:
  - Agent settings form
  - Tool permissions
  - Custom prompt editing
  - Save/cancel actions
- **State**: Agent config, form validation

## Command System Components

### CommandPalette
- **Purpose**: Command execution interface
- **Features**:
  - Command search and autocomplete
  - Built-in and custom commands
  - Command history
  - Keyboard shortcuts
- **State**: Available commands, search query, history

### CommandInput
- **Purpose**: Command line interface
- **Features**:
  - Command parsing and validation
  - Argument completion
  - Command history
  - Execute button
- **State**: Command text, validation, history

### ShellInterface
- **Purpose**: Shell command execution
- **Features**:
  - Terminal-like interface
  - Command input
  - Output display
  - Command history
- **State**: Command history, current output

## Permission & Dialog Components

### PermissionDialog
- **Purpose**: Handle permission requests
- **Features**:
  - Permission details display
  - Allow/deny buttons
  - Remember choice option
  - Context information
- **State**: Permission request, user choice

### ConfirmDialog
- **Purpose**: Confirmation dialogs
- **Features**:
  - Customizable message
  - Yes/no buttons
  - Optional details
- **State**: Dialog content, user choice

### ToastNotifications
- **Purpose**: Show temporary notifications
- **Features**:
  - Success/error/warning/info types
  - Auto-dismiss timer
  - Manual dismiss
  - Stack multiple toasts
- **State**: Active toasts, timers

## Settings & Configuration

### SettingsPanel
- **Purpose**: Application configuration
- **Features**:
  - Server connection settings
  - UI preferences
  - Keyboard shortcuts
  - Theme selection
- **State**: Settings values, validation

### ThemeSelector
- **Purpose**: Theme selection and customization
- **Features**:
  - Built-in themes
  - Custom theme creation
  - Preview mode
- **State**: Available themes, selected theme

## Utility Components

### LoadingSpinner
- **Purpose**: Loading state indicator
- **Features**:
  - Various sizes
  - Customizable colors
  - Optional text

### ErrorBoundary
- **Purpose**: Error handling
- **Features**:
  - Catch React errors
  - Display error message
  - Retry functionality

### VirtualList
- **Purpose**: Performance for large lists
- **Features**:
  - Virtual scrolling
  - Dynamic item heights
  - Smooth scrolling

### CodeHighlighter
- **Purpose**: Syntax highlighting
- **Features**:
  - Multiple language support
  - Theme integration
  - Copy code button

### MarkdownRenderer
- **Purpose**: Render markdown content
- **Features**:
  - Standard markdown support
  - Code block highlighting
  - Link handling

## Component Hierarchy

```
App
├── AppHeader
│   ├── Logo
│   ├── SessionTitle
│   ├── AgentSelector
│   ├── ModelSelector
│   └── ConnectionStatus
├── AppSidebar
│   ├── SessionList
│   │   └── SessionItem[]
│   ├── FileExplorer
│   │   └── FileItem[]
│   └── SettingsPanel
└── MainContent
    ├── ChatContainer
    │   ├── MessageList
    │   │   └── MessageItem[]
    │   │       └── MessageParts[]
    │   └── MessageInput
    ├── FileViewer
    └── FileEditor
```

## State Management Integration

Each component connects to the global state through:
- **React Context** for theme and configuration
- **Zustand/Redux** for application state
- **React Query** for server state
- **Local state** for UI-specific state

## Responsive Design

All components should be responsive and work on:
- Desktop (primary target)
- Tablet (secondary)
- Mobile (basic functionality)

Key breakpoints:
- Mobile: < 768px
- Tablet: 768px - 1024px
- Desktop: > 1024px
