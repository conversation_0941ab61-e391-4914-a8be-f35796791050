# Technical Specifications

Detailed technical requirements, architecture decisions, and implementation specifications for the OpenCode Web UI.

## Technology Stack

### Frontend Framework
- **React 18+** with TypeScript
- **Next.js 14+** for SSR/SSG capabilities
- **Tailwind CSS** for styling
- **Framer Motion** for animations

### State Management
- **Zustand** for global application state
- **React Query (TanStack Query)** for server state management
- **React Context** for theme and configuration
- **Local Storage** for persistence

### UI Components
- **Headless UI** for accessible components
- **Monaco Editor** for code editing
- **React Virtual** for large list virtualization
- **React Markdown** for markdown rendering
- **Prism.js** for syntax highlighting

### Development Tools
- **Vite** for fast development and building
- **TypeScript 5+** for type safety
- **ESLint + Prettier** for code quality
- **Vitest** for unit testing
- **Playwright** for E2E testing
- **Storybook** for component development

## Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    Web UI Application                       │
├─────────────────────────────────────────────────────────────┤
│  Presentation Layer (React Components)                     │
│  ├── Layout Components (<PERSON><PERSON>, Sidebar, Main)             │
│  ├── Feature Components (Chat, Files, Sessions)            │
│  └── UI Components (Buttons, Modals, Forms)                │
├─────────────────────────────────────────────────────────────┤
│  State Management Layer                                     │
│  ├── Application State (Zustand)                           │
│  ├── Server State (React Query)                            │
│  └── Local State (React useState/useReducer)               │
├─────────────────────────────────────────────────────────────┤
│  API Layer                                                  │
│  ├── HTTP Client (Fetch/Axios)                             │
│  ├── WebSocket/SSE Client                                  │
│  └── API Hooks (React Query)                               │
├─────────────────────────────────────────────────────────────┤
│  Utility Layer                                             │
│  ├── Type Definitions                                      │
│  ├── Helper Functions                                      │
│  └── Constants                                             │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                OpenCode API Server                         │
│  (Existing - packages/opencode/src/server)                 │
└─────────────────────────────────────────────────────────────┘
```

## Project Structure

```
src/
├── components/           # React components
│   ├── ui/              # Basic UI components
│   ├── layout/          # Layout components
│   ├── chat/            # Chat-related components
│   ├── files/           # File management components
│   ├── sessions/        # Session management components
│   └── settings/        # Settings components
├── hooks/               # Custom React hooks
├── stores/              # Zustand stores
├── api/                 # API client and hooks
├── types/               # TypeScript type definitions
├── utils/               # Utility functions
├── styles/              # Global styles and themes
├── constants/           # Application constants
└── pages/               # Next.js pages (if using App Router)
```

## State Management Architecture

### Global State (Zustand)
```typescript
interface AppStore {
  // Connection state
  connected: boolean
  serverUrl: string
  
  // UI state
  sidebarOpen: boolean
  currentView: 'chat' | 'files' | 'settings'
  theme: string
  
  // Current selections
  currentSession: Session | null
  selectedAgent: string
  selectedModel: string
  
  // Actions
  setConnected: (connected: boolean) => void
  setSidebarOpen: (open: boolean) => void
  setCurrentSession: (session: Session | null) => void
  // ... other actions
}
```

### Server State (React Query)
```typescript
// Query keys
const queryKeys = {
  sessions: ['sessions'] as const,
  session: (id: string) => ['sessions', id] as const,
  messages: (sessionId: string) => ['sessions', sessionId, 'messages'] as const,
  files: (path: string) => ['files', path] as const,
  // ... other keys
}

// Custom hooks
const useSessions = () => useQuery({
  queryKey: queryKeys.sessions,
  queryFn: () => api.getSessions(),
})

const useMessages = (sessionId: string) => useQuery({
  queryKey: queryKeys.messages(sessionId),
  queryFn: () => api.getMessages(sessionId),
  enabled: !!sessionId,
})
```

## API Client Architecture

### HTTP Client
```typescript
class ApiClient {
  private baseUrl: string
  private directory?: string
  
  constructor(baseUrl: string, directory?: string) {
    this.baseUrl = baseUrl
    this.directory = directory
  }
  
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = new URL(endpoint, this.baseUrl)
    if (this.directory) {
      url.searchParams.set('directory', this.directory)
    }
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    })
    
    if (!response.ok) {
      throw new ApiError(response.status, await response.json())
    }
    
    return response.json()
  }
  
  // Session methods
  async getSessions(): Promise<Session[]> {
    return this.request('/session')
  }
  
  async createSession(data: CreateSessionData): Promise<Session> {
    return this.request('/session', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }
  
  // ... other methods
}
```

### Server-Sent Events
```typescript
class SSEClient {
  private eventSource: EventSource | null = null
  private listeners: Map<string, Set<(data: any) => void>> = new Map()
  
  connect(url: string) {
    this.eventSource = new EventSource(url)
    
    this.eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data)
      this.emit(data.type, data.properties)
    }
    
    this.eventSource.onerror = () => {
      // Handle reconnection
    }
  }
  
  on(eventType: string, callback: (data: any) => void) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set())
    }
    this.listeners.get(eventType)!.add(callback)
  }
  
  private emit(eventType: string, data: any) {
    const callbacks = this.listeners.get(eventType)
    if (callbacks) {
      callbacks.forEach(callback => callback(data))
    }
  }
}
```

## Component Architecture

### Component Patterns
1. **Container Components**: Handle state and business logic
2. **Presentational Components**: Handle UI rendering
3. **Custom Hooks**: Encapsulate reusable logic
4. **Higher-Order Components**: For cross-cutting concerns

### Example Component Structure
```typescript
// Container Component
export const ChatContainer: React.FC = () => {
  const { currentSession } = useAppStore()
  const { data: messages, isLoading } = useMessages(currentSession?.id)
  const sendMessage = useSendMessage()
  
  return (
    <ChatView
      messages={messages}
      loading={isLoading}
      onSendMessage={sendMessage}
    />
  )
}

// Presentational Component
interface ChatViewProps {
  messages: MessageWithParts[]
  loading: boolean
  onSendMessage: (message: string) => void
}

export const ChatView: React.FC<ChatViewProps> = ({
  messages,
  loading,
  onSendMessage,
}) => {
  return (
    <div className="flex flex-col h-full">
      <MessageList messages={messages} loading={loading} />
      <MessageInput onSend={onSendMessage} />
    </div>
  )
}
```

## Performance Optimizations

### Virtual Scrolling
```typescript
import { FixedSizeList as List } from 'react-window'

const MessageList: React.FC<{ messages: Message[] }> = ({ messages }) => {
  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => (
    <div style={style}>
      <MessageItem message={messages[index]} />
    </div>
  )
  
  return (
    <List
      height={600}
      itemCount={messages.length}
      itemSize={100}
      width="100%"
    >
      {Row}
    </List>
  )
}
```

### Memoization
```typescript
const MessageItem = React.memo<{ message: Message }>(({ message }) => {
  const formattedTime = useMemo(
    () => formatTime(message.time.created),
    [message.time.created]
  )
  
  return (
    <div className="message">
      <span>{formattedTime}</span>
      <MessageContent content={message.content} />
    </div>
  )
})
```

### Code Splitting
```typescript
// Lazy load heavy components
const FileEditor = lazy(() => import('./FileEditor'))
const SettingsPanel = lazy(() => import('./SettingsPanel'))

// Use with Suspense
<Suspense fallback={<LoadingSpinner />}>
  <FileEditor />
</Suspense>
```

## Error Handling

### Error Boundary
```typescript
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props)
    this.state = { hasError: false }
  }
  
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }
  
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo)
    // Send to error reporting service
  }
  
  render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} />
    }
    
    return this.props.children
  }
}
```

### API Error Handling
```typescript
class ApiError extends Error {
  constructor(
    public status: number,
    public data: any,
    message?: string
  ) {
    super(message || `API Error: ${status}`)
  }
}

// In React Query
const useMessages = (sessionId: string) => useQuery({
  queryKey: ['messages', sessionId],
  queryFn: () => api.getMessages(sessionId),
  onError: (error: ApiError) => {
    if (error.status === 401) {
      // Handle authentication error
    } else if (error.status >= 500) {
      // Handle server error
    }
    // Show error toast
  },
})
```

## Security Considerations

### Input Sanitization
```typescript
import DOMPurify from 'dompurify'

const sanitizeHtml = (html: string): string => {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'code', 'pre'],
    ALLOWED_ATTR: ['class'],
  })
}
```

### XSS Prevention
- Sanitize all user inputs
- Use React's built-in XSS protection
- Validate API responses
- Use Content Security Policy headers

### Authentication
```typescript
// Store auth tokens securely
const useAuth = () => {
  const [token, setToken] = useState<string | null>(
    localStorage.getItem('auth_token')
  )
  
  const login = (credentials: LoginCredentials) => {
    // Authenticate and store token
  }
  
  const logout = () => {
    localStorage.removeItem('auth_token')
    setToken(null)
  }
  
  return { token, login, logout }
}
```

## Testing Strategy

### Unit Tests
```typescript
import { render, screen, fireEvent } from '@testing-library/react'
import { MessageInput } from './MessageInput'

describe('MessageInput', () => {
  it('should send message on enter key', () => {
    const onSend = jest.fn()
    render(<MessageInput onSend={onSend} />)
    
    const input = screen.getByRole('textbox')
    fireEvent.change(input, { target: { value: 'Hello' } })
    fireEvent.keyDown(input, { key: 'Enter' })
    
    expect(onSend).toHaveBeenCalledWith('Hello')
  })
})
```

### Integration Tests
```typescript
import { renderWithProviders } from '../test-utils'
import { ChatContainer } from './ChatContainer'

describe('ChatContainer', () => {
  it('should display messages and allow sending new ones', async () => {
    const { user } = renderWithProviders(<ChatContainer />)
    
    // Wait for messages to load
    await screen.findByText('Previous message')
    
    // Send new message
    const input = screen.getByRole('textbox')
    await user.type(input, 'New message')
    await user.keyboard('{Enter}')
    
    // Verify message was sent
    expect(screen.getByText('New message')).toBeInTheDocument()
  })
})
```

### E2E Tests
```typescript
import { test, expect } from '@playwright/test'

test('should create new session and send message', async ({ page }) => {
  await page.goto('/')
  
  // Create new session
  await page.click('[data-testid="new-session"]')
  await page.fill('[data-testid="session-title"]', 'Test Session')
  await page.click('[data-testid="create-session"]')
  
  // Send message
  await page.fill('[data-testid="message-input"]', 'Hello, AI!')
  await page.press('[data-testid="message-input"]', 'Enter')
  
  // Verify message appears
  await expect(page.locator('text=Hello, AI!')).toBeVisible()
})
```

## Deployment Configuration

### Build Configuration
```typescript
// vite.config.ts
export default defineConfig({
  plugins: [react()],
  build: {
    target: 'es2020',
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          editor: ['monaco-editor'],
        },
      },
    },
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
      },
    },
  },
})
```

### Environment Configuration
```typescript
// Environment variables
interface Config {
  API_BASE_URL: string
  WS_URL: string
  NODE_ENV: 'development' | 'production' | 'test'
  VERSION: string
}

const config: Config = {
  API_BASE_URL: process.env.VITE_API_BASE_URL || 'http://localhost:3000',
  WS_URL: process.env.VITE_WS_URL || 'ws://localhost:3000',
  NODE_ENV: process.env.NODE_ENV || 'development',
  VERSION: process.env.VITE_VERSION || '1.0.0',
}
```

## Browser Support

### Target Browsers
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### Polyfills
- Core-js for ES6+ features
- Fetch polyfill for older browsers
- ResizeObserver polyfill
- IntersectionObserver polyfill

### Progressive Enhancement
- Basic functionality without JavaScript
- Enhanced experience with JavaScript enabled
- Graceful degradation for unsupported features
