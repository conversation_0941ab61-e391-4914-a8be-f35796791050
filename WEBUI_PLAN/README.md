# OpenCode Web UI Plan

This directory contains the comprehensive plan for building a Web UI that perfectly mirrors the TUI functionality of OpenCode.

## Overview

OpenCode is an AI coding agent built for the terminal with a client/server architecture. The TUI is just one client - we're building a Web UI client that interacts with the same API server.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web UI        │    │   API Server    │    │   TUI Client    │
│   (New)         │◄──►│   (Existing)    │◄──►│   (Existing)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

The Web UI will:
- Connect to the existing OpenCode API server
- Provide the same functionality as the TUI
- Use modern web technologies (React/Vue/Svelte + TypeScript)
- Support real-time updates via Server-Sent Events
- Handle all the same interactions and workflows

## Key Files in This Plan

1. **[API_ENDPOINTS.md](./API_ENDPOINTS.md)** - Complete API reference with all endpoints
2. **[COMPONENTS.md](./COMPONENTS.md)** - UI component breakdown and structure
3. **[INTERACTIONS.md](./INTERACTIONS.md)** - All user interactions and workflows
4. **[DATA_MODELS.md](./DATA_MODELS.md)** - Data structures and state management
5. **[IMPLEMENTATION_PLAN.md](./IMPLEMENTATION_PLAN.md)** - Step-by-step development plan
6. **[TECHNICAL_SPECS.md](./TECHNICAL_SPECS.md)** - Technical requirements and decisions

## Core Functionality to Implement

### Session Management
- Create, list, select, delete sessions
- Session sharing and unsharing
- Session hierarchy (parent/child relationships)
- Session initialization and summarization

### Chat Interface
- Message display with syntax highlighting
- Real-time message streaming
- Message parts (text, code, tool calls, etc.)
- Message history and navigation
- Copy/paste functionality

### Agent & Model Management
- Agent selection (build, plan, general, custom)
- Model/provider selection
- Agent configuration and permissions
- Custom agent creation

### File Operations
- File browser and navigation
- File reading, writing, editing
- File search (content and filename)
- Git integration and diff display
- File status and change tracking

### Command System
- Built-in commands (/init, /undo, /redo, /share, /help)
- Custom command creation and execution
- Shell command execution
- Command history and autocomplete

### Tool Integration
- All built-in tools (bash, read, write, edit, etc.)
- Tool permission management
- Tool execution and output display
- Custom tool support

### Real-time Features
- Live message updates
- File change notifications
- Session state synchronization
- Permission request handling

## Technology Stack Recommendations

### Frontend Framework
- **React** with TypeScript (most mature ecosystem)
- **Next.js** for SSR/SSG capabilities
- **Tailwind CSS** for styling (matches terminal aesthetic)

### State Management
- **Zustand** or **Redux Toolkit** for global state
- **React Query/TanStack Query** for API state management
- **WebSocket/SSE** for real-time updates

### UI Components
- **Headless UI** or **Radix UI** for accessible components
- **Monaco Editor** for code editing
- **React Virtual** for large lists
- **Framer Motion** for animations

### Development Tools
- **Vite** for fast development
- **TypeScript** for type safety
- **ESLint + Prettier** for code quality
- **Storybook** for component development

## Getting Started

1. Read through all the plan documents
2. Set up the development environment
3. Start with the basic layout and session management
4. Implement the chat interface
5. Add file operations and tool integration
6. Implement real-time features
7. Add advanced features and polish

The plan is designed to be implemented incrementally, with each phase building on the previous one.
