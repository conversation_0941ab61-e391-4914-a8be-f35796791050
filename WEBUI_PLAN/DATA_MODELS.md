# Data Models & Types

Complete TypeScript type definitions for all data structures used in the OpenCode Web UI.

## Core Types

### Session
```typescript
interface Session {
  id: string
  projectID: string
  directory: string
  parentID?: string
  share?: {
    url: string
  }
  title: string
  version: string
  time: {
    created: number
    updated: number
    compacting?: number
  }
  revert?: {
    messageID: string
    partID?: string
    snapshot?: string
    diff?: string
  }
}
```

### Message Types
```typescript
interface BaseMessage {
  id: string
  sessionID: string
  role: "user" | "assistant"
}

interface UserMessage extends BaseMessage {
  role: "user"
  time: {
    created: number
  }
}

interface AssistantMessage extends BaseMessage {
  role: "assistant"
  time: {
    created: number
    completed?: number
  }
  error?: ErrorType
  system: string[]
  modelID: string
  providerID: string
  mode: string
  path: {
    cwd: string
    root: string
  }
  summary?: boolean
  cost: number
  tokens: {
    input: number
    output: number
    reasoning: number
    cache: {
      read: number
      write: number
    }
  }
}

type Message = UserMessage | AssistantMessage

interface MessageWithParts {
  info: Message
  parts: Part[]
}
```

### Message Parts
```typescript
interface BasePart {
  id: string
  messageID: string
  sessionID: string
  synthetic?: boolean
}

interface TextPart extends BasePart {
  type: "text"
  text: string
}

interface ReasoningPart extends BasePart {
  type: "reasoning"
  text: string
}

interface ToolInvocationPart extends BasePart {
  type: "tool-invocation"
  toolCallId: string
  toolName: string
  args: Record<string, any>
  state: {
    status: "running" | "success" | "error"
    metadata?: Record<string, any>
  }
  result?: {
    title: string
    output: string
    metadata: Record<string, any>
  }
}

interface SourceUrlPart extends BasePart {
  type: "source-url"
  url: string
  title?: string
}

interface FilePart extends BasePart {
  type: "file"
  path: string
  content: string
  language?: string
}

interface StepStartPart extends BasePart {
  type: "step-start"
  step: number
}

interface StepFinishPart extends BasePart {
  type: "step-finish"
  cost: number
  tokens: {
    input: number
    output: number
    reasoning: number
    cache: {
      read: number
      write: number
    }
  }
}

type Part = TextPart | ReasoningPart | ToolInvocationPart | SourceUrlPart | FilePart | StepStartPart | StepFinishPart
```

### Agent & Model Types
```typescript
interface Agent {
  name: string
  description?: string
  mode: "subagent" | "primary" | "all"
  builtIn: boolean
  topP?: number
  temperature?: number
  permission: {
    edit: Permission
    bash: Record<string, Permission>
    webfetch?: Permission
  }
  model?: {
    modelID: string
    providerID: string
  }
  prompt?: string
  tools: Record<string, boolean>
  options: Record<string, any>
}

interface Provider {
  id: string
  name: string
  models: Model[]
}

interface Model {
  id: string
  name: string
  limit: {
    input: number
    output: number
  }
  cost: {
    input: number
    output: number
  }
}

type Permission = "allow" | "deny" | "ask"
```

### File System Types
```typescript
interface FileNode {
  name: string
  path: string
  absolute: string
  type: "file" | "directory"
  ignored: boolean
}

interface FileContent {
  content: string
  patch?: string
  diff?: string
}

interface FileInfo {
  path: string
  status: string
  staged: boolean
}

interface SearchMatch {
  path: {
    text: string
  }
  lines: {
    line_number: number
    text: string
  }
  line_number: number
  absolute_offset: number
  submatches: Array<{
    match: {
      text: string
    }
    start: number
    end: number
  }>
}

interface Symbol {
  name: string
  kind: string
  location: {
    uri: string
    range: {
      start: { line: number; character: number }
      end: { line: number; character: number }
    }
  }
}
```

### Command & Tool Types
```typescript
interface Command {
  name: string
  description?: string
  agent?: string
  model?: string
  template: string
  subtask?: boolean
}

interface ToolListItem {
  id: string
  description: string
  parameters: any // JSON Schema
}
```

### Configuration Types
```typescript
interface Config {
  providers: Provider[]
  default: Record<string, string>
  // ... other config properties
}

interface PathInfo {
  state: string
  config: string
  worktree: string
  directory: string
}
```

### Error Types
```typescript
interface ProviderAuthError {
  name: "ProviderAuthError"
  data: {
    providerID: string
    message: string
  }
}

interface UnknownError {
  name: "UnknownError"
  data: {
    message: string
  }
}

interface MessageOutputLengthError {
  name: "MessageOutputLengthError"
  data: {}
}

interface AbortedError {
  name: "AbortedError"
  data: {}
}

type ErrorType = ProviderAuthError | UnknownError | MessageOutputLengthError | AbortedError
```

### Permission Types
```typescript
interface Permission {
  id: string
  sessionID: string
  messageID: string
  callID: string
  type: string
  title: string
  metadata: Record<string, any>
  time: {
    created: number
  }
}

type PermissionResponse = "allow" | "deny" | "always" | "never"
```

### Event Types
```typescript
interface ServerConnectedEvent {
  type: "server.connected"
  properties: {}
}

interface MessageUpdatedEvent {
  type: "message.updated"
  properties: {
    info: Message
  }
}

interface MessagePartUpdatedEvent {
  type: "message.part.updated"
  properties: {
    part: Part
  }
}

interface SessionUpdatedEvent {
  type: "session.updated"
  properties: {
    session: Session
  }
}

interface FileWatcherUpdatedEvent {
  type: "file.watcher.updated"
  properties: {
    file: string
    event: "add" | "change" | "unlink"
  }
}

type Event = ServerConnectedEvent | MessageUpdatedEvent | MessagePartUpdatedEvent | SessionUpdatedEvent | FileWatcherUpdatedEvent
```

### Input Types
```typescript
interface PromptInput {
  sessionID: string
  messageID?: string
  agent?: string
  model?: string
  system?: string[]
  tools?: Record<string, boolean>
  parts: Array<{
    type: "text" | "file" | "agent"
    text?: string
    path?: string
    content?: string
    name?: string
  }>
}

interface CommandInput {
  sessionID: string
  messageID?: string
  agent?: string
  model?: string
  command: string
  arguments: string
}

interface ShellInput {
  sessionID: string
  agent: string
  command: string
}
```

## State Management Types

```typescript
interface AppState {
  // Connection
  connected: boolean
  serverUrl: string

  // Current context
  currentSession: Session | null
  currentDirectory: string

  // UI state
  sidebarOpen: boolean
  currentView: "chat" | "files" | "settings"

  // Selections
  selectedAgent: string
  selectedProvider: string
  selectedModel: string
}

interface SessionState {
  sessions: Session[]
  messages: Record<string, MessageWithParts[]>
  loading: Record<string, boolean>
  permissions: Record<string, Permission[]>
}

interface FileState {
  currentPath: string
  files: FileNode[]
  fileContent: Record<string, FileContent>
  searchResults: SearchMatch[]
  symbols: Symbol[]
}

interface ConfigState {
  config: Config | null
  agents: Agent[]
  commands: Command[]
  tools: ToolListItem[]
  providers: Provider[]
}
```

## Usage Notes

- All timestamps are Unix timestamps (milliseconds)
- File paths are relative to the project directory unless absolute
- Tool parameters follow JSON Schema format
- Event stream uses Server-Sent Events format
- All API responses include proper error handling

## State Management Types

### Application State
```typescript
interface AppState {
  // Connection
  connected: boolean
  serverUrl: string
  
  // Current context
  currentSession: Session | null
  currentDirectory: string
  
  // UI state
  sidebarOpen: boolean
  currentView: "chat" | "files" | "settings"
  
  // Selections
  selectedAgent: string
  selectedProvider: string
  selectedModel: string
}

interface SessionState {
  sessions: Session[]
  messages: Record<string, MessageWithParts[]>
  loading: Record<string, boolean>
  permissions: Record<string, Permission[]>
}

interface FileState {
  currentPath: string
  files: FileNode[]
  fileContent: Record<string, FileContent>
  searchResults: SearchMatch[]
  symbols: Symbol[]
}

interface ConfigState {
  config: Config | null
  agents: Agent[]
  commands: Command[]
  tools: ToolListItem[]
  providers: Provider[]
}
```
