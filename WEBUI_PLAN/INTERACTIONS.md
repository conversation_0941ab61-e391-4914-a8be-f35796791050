# User Interactions & Workflows

Complete documentation of all user interactions and workflows in the OpenCode Web UI, mirroring the TUI functionality.

## Core Interaction Patterns

### Session Management Workflows

#### Creating a New Session
1. **Trigger**: Click "New Session" button or use keyboard shortcut
2. **Dialog**: Session creation modal opens
3. **Inputs**: 
   - Optional parent session selection
   - Optional custom title
   - Agent selection (defaults to "build")
   - Model selection (defaults to provider default)
4. **Action**: POST `/session` with parameters
5. **Result**: New session created and automatically selected
6. **Navigation**: Switch to chat view for new session

#### Selecting a Session
1. **Trigger**: Click session in sidebar list
2. **Action**: GET `/session/:id/message` to load messages
3. **Result**: Chat view updates with session messages
4. **State**: Current session updated in global state

#### Deleting a Session
1. **Trigger**: Right-click session → Delete or delete button
2. **Confirmation**: "Are you sure?" dialog
3. **Action**: DELETE `/session/:id`
4. **Result**: Session removed from list
5. **Navigation**: If current session deleted, switch to most recent

#### Sharing a Session
1. **Trigger**: Click share button or right-click → Share
2. **Action**: POST `/session/:id/share`
3. **Result**: Share URL generated and copied to clipboard
4. **Feedback**: Toast notification with share link

#### Renaming a Session
1. **Trigger**: Double-click session title or right-click → Rename
2. **Input**: Inline text editor appears
3. **Action**: PATCH `/session/:id` with new title
4. **Result**: Session title updated in list and header

### Chat Interface Workflows

#### Sending a Message
1. **Input**: Type message in input area
2. **Attachments**: Optional file attachments via drag-drop or button
3. **Trigger**: Press Enter, Ctrl+Enter, or click Send button
4. **Validation**: Check message not empty
5. **Action**: POST `/session/:id/message` with message data
6. **UI Updates**:
   - Message appears in chat immediately (optimistic update)
   - Input cleared
   - Auto-scroll to bottom
   - Loading indicator for AI response
7. **Real-time**: Listen for message updates via SSE

#### Executing Commands
1. **Input**: Type command starting with `/` (e.g., `/help`, `/init`)
2. **Autocomplete**: Show available commands as user types
3. **Validation**: Validate command exists and arguments
4. **Action**: POST `/session/:id/command` with command and args
5. **Result**: Command execution shown as tool invocation
6. **Output**: Command results displayed in chat

#### Shell Command Execution
1. **Input**: Type shell command in special shell mode or via `/shell`
2. **Trigger**: Execute button or Enter
3. **Action**: POST `/session/:id/shell` with command
4. **Real-time**: Stream command output as it executes
5. **Result**: Final output and exit code displayed

#### Message Actions
- **Copy Message**: Click copy button → copy to clipboard
- **Revert Message**: Click revert button → POST `/session/:id/revert`
- **Undo Last**: Keyboard shortcut → revert last assistant message
- **Redo**: Keyboard shortcut → POST `/session/:id/unrevert`

### File Management Workflows

#### File Navigation
1. **Trigger**: Click folder in file explorer
2. **Action**: GET `/file?path=folder_path`
3. **Result**: Folder contents displayed in tree
4. **State**: Current path updated

#### Opening Files
1. **Trigger**: Click file in explorer
2. **Action**: GET `/file/content?path=file_path`
3. **Result**: File content displayed in viewer
4. **Features**: Syntax highlighting, line numbers, git diff

#### File Search
1. **Input**: Type search query in search box
2. **Types**: 
   - Content search: GET `/find?pattern=query`
   - Filename search: GET `/find/file?query=filename`
   - Symbol search: GET `/find/symbol?query=symbol`
3. **Results**: Display matches with context
4. **Navigation**: Click result to open file at location

#### File Operations
- **Create File**: Right-click → New File → name input
- **Create Folder**: Right-click → New Folder → name input
- **Delete**: Right-click → Delete → confirmation
- **Rename**: Right-click → Rename → inline editor

### Agent & Model Management

#### Selecting Agent
1. **Trigger**: Click agent selector in header
2. **Dialog**: Agent selection modal opens
3. **Display**: 
   - Built-in agents (build, plan, general)
   - Custom agents
   - Agent descriptions and capabilities
   - Recently used agents at top
4. **Selection**: Click agent to select
5. **Result**: Agent updated for current session

#### Selecting Model
1. **Trigger**: Click model selector in header
2. **Dialog**: Model selection modal opens
3. **Display**:
   - Grouped by provider
   - Model capabilities and costs
   - Recently used models at top
4. **Selection**: Click model to select
5. **Result**: Model updated for current session

#### Creating Custom Agent
1. **Trigger**: Click "Create Agent" in agent selector
2. **Form**: Agent configuration form
3. **Inputs**:
   - Agent name and description
   - Tool permissions
   - Custom system prompt
   - Default model
4. **Action**: Save agent configuration
5. **Result**: New agent available in selector

### Permission Handling

#### Permission Requests
1. **Trigger**: AI attempts restricted action (file write, shell command)
2. **Dialog**: Permission request modal appears
3. **Display**:
   - Action description
   - Context information
   - Allow/Deny buttons
   - "Remember this choice" option
4. **Response**: POST `/session/:id/permissions/:permissionID`
5. **Result**: Action proceeds or is blocked

### Search & Discovery

#### Global Search
1. **Trigger**: Keyboard shortcut (Ctrl+K) or search button
2. **Interface**: Command palette style search
3. **Scope**: 
   - Files and content
   - Commands
   - Sessions
   - Symbols
4. **Navigation**: Arrow keys + Enter to select

#### File Content Search
1. **Input**: Search query in file explorer
2. **Action**: GET `/find?pattern=query`
3. **Results**: Files with matches, line numbers, context
4. **Navigation**: Click to open file at match location

### Real-time Features

#### Live Message Updates
1. **Connection**: SSE connection to `/event`
2. **Events**: Listen for `message.updated` and `message.part.updated`
3. **Updates**: Update UI in real-time as AI generates response
4. **Streaming**: Show partial responses as they arrive

#### File Change Notifications
1. **Events**: Listen for `file.watcher.updated`
2. **Indicators**: Show file status changes in explorer
3. **Refresh**: Auto-refresh file content if currently viewing

#### Session Synchronization
1. **Events**: Listen for `session.updated`
2. **Updates**: Sync session state across tabs/windows
3. **Conflicts**: Handle concurrent edits gracefully

### Keyboard Shortcuts

#### Global Shortcuts
- `Ctrl+K`: Open command palette
- `Ctrl+N`: New session
- `Ctrl+Shift+P`: Open command palette
- `Ctrl+,`: Open settings
- `Ctrl+B`: Toggle sidebar
- `Ctrl+\`: Toggle file explorer

#### Chat Shortcuts
- `Enter`: Send message (if not multiline)
- `Ctrl+Enter`: Send message (always)
- `Shift+Enter`: New line
- `Ctrl+Z`: Undo last message
- `Ctrl+Shift+Z`: Redo last message
- `Up/Down`: Navigate message history
- `Ctrl+L`: Clear chat

#### File Shortcuts
- `Ctrl+O`: Open file
- `Ctrl+S`: Save file (if editing)
- `Ctrl+F`: Search in file
- `Ctrl+Shift+F`: Global search
- `F2`: Rename file

### Error Handling

#### Connection Errors
1. **Detection**: API request fails or SSE disconnects
2. **Indicator**: Connection status shows "Disconnected"
3. **Retry**: Automatic reconnection attempts
4. **Fallback**: Offline mode with cached data

#### API Errors
1. **Display**: Error toast notifications
2. **Context**: Show error details in relevant component
3. **Recovery**: Retry buttons where appropriate
4. **Logging**: Send errors to `/log` endpoint

#### Validation Errors
1. **Real-time**: Validate inputs as user types
2. **Display**: Inline error messages
3. **Prevention**: Disable submit buttons until valid
4. **Guidance**: Helpful error messages and suggestions

### Accessibility

#### Keyboard Navigation
- All interactive elements accessible via keyboard
- Logical tab order
- Focus indicators
- Escape key to close modals/dialogs

#### Screen Reader Support
- Proper ARIA labels and roles
- Live regions for dynamic content
- Descriptive text for icons and buttons

#### Visual Accessibility
- High contrast mode support
- Scalable fonts and UI elements
- Color-blind friendly color schemes

### Mobile Adaptations

#### Touch Interactions
- Tap to select/open
- Long press for context menus
- Swipe gestures for navigation
- Pull to refresh

#### Layout Adaptations
- Collapsible sidebar becomes bottom sheet
- Stack layout for narrow screens
- Touch-friendly button sizes
- Simplified navigation

### Performance Optimizations

#### Virtual Scrolling
- Large message lists use virtual scrolling
- File explorer virtualizes large directories
- Search results paginated

#### Lazy Loading
- Load message history on demand
- Lazy load file content
- Progressive image loading

#### Caching
- Cache API responses
- Store session state locally
- Offline-first approach where possible

### Integration Features

#### Copy/Paste
- Copy messages, code blocks, file content
- Paste files as attachments
- Rich text paste handling

#### Drag & Drop
- Drag files to attach to messages
- Drag files between folders
- Drag to reorder sessions

#### External Links
- Open external URLs in new tabs
- Handle file:// links appropriately
- Integration with system file manager
